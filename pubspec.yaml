name: vcb
description: "VCB"
publish_to: 'none' 

version: 1.0.0+1

environment:
  sdk: ">=3.8.0 <4.0.0"
  flutter: ">=3.3.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8
  #GetX状态管理框架 https://pub.flutter-io.cn/packages/get
  get: ^4.7.2
  # 网络请求库 https://github.com/flutterchina/
  dio: ^5.9.0
   # dio缓存库 https://pub.dev/packages/dio_cache_interceptor
  dio_cache_interceptor: ^4.0.3
  # 网络请求库 https://pub-web.flutter-io.cn/packages/retrofit
  retrofit: ^4.7.1
  #屏幕适配 https://pub-web.flutter-io.cn/packages/flutter_screenutil
  flutter_screenutil: ^5.9.3
  #常用工具类库 common_utils   https://github.com/Sky24n/common_utils
  common_utils: ^2.1.0
  #大数字精度处理 https://pub.dev/packages/decimal
  decimal: ^3.2.1
  big_decimal: ^0.5.0
  # 提供国际化和本地化功能，包括消息翻译、复数和性别、金额千分位 日期/数字格式和解析 DateFormat、NumberFormat和BidiFormatter类。 https://pub.dev/packages/intl
  intl: ^0.20.2
  # Loading https://pub.dev/packages/flutter_easyloading
  flutter_easyloading: ^3.0.5
  #设备类型 https://pub.dev/packages/device_info_plus
  device_info_plus: ^11.5.0
  #URL验证 https://pub.dev/packages/regexed_validator/install
  regexed_validator: ^2.0.0+1
  #网络响应日志打印框架 https://pub-web.flutter-io.cn/packages/pretty_dio_logger
  pretty_dio_logger: ^1.4.0
  #普通日志打印框架  https://pub-web.flutter-io.cn/packages/logger
  logger: ^2.6.1
  # 应用中获取版本号和基本信息 https://pub.dev/packages/package_info_plus/install
  package_info_plus: ^8.3.0
  #权限申请框架 https://pub.flutter-io.cn/packages/permission_handler
  permission_handler: ^12.0.1
  # 安全存储 Android Keystore / ios Keychain 保护https://pub.dev/packages/flutter_secure_storage/install
  flutter_secure_storage: ^9.0.0
  # 加密库https://pub.dev/packages/encrypt
  encrypt: ^5.0.3
  # string转16进制字符 https://pub.dev/packages/hex
  hex: ^0.2.0
  # Android内升级
  flutter_app_update: ^3.2.2
  #将密钥存储在 .env 文件中 https://pub.dev/packages/flutter_dotenv/install
  flutter_dotenv: ^5.2.1
  # Bugly
  flutter_bugly: ^1.1.0
  #ORM数据库 https://pub.dev/packages/drift
  drift: ^2.28.1
  #提供 sqlite 动态库，如果要加密数据库，请使用 sqlcipher_flutter_libs
  sqlite3_flutter_libs: ^0.5.38
  #播放声音 https://pub.dev/packages/audioplayers
  audioplayers: ^6.5.0
  # https://pub.dev/packages/screen_brightness
  screen_brightness: ^2.1.4
  #GetX提供的KeyValue存储 https://pub-web.flutter-io.cn/packages/get_storage
  get_storage: ^2.1.1
  # 打开网页、发起电话、发送电子邮件以及与其他应用程序交互，如打开地图或播放器应，或为URL配置通用和深度链接https://github.com/flutter/plugins/tree/master/packages/url_launcher
  url_launcher: ^6.3.2
  #https://pub.dev/packages/pull_to_refresh
  pull_to_refresh: ^2.0.0
  #悬浮窗引导 https://pub.dev/packages/showcaseview/install
  showcaseview: ^4.0.1
  #Toast https://pub.dev/packages/shimmer_animation/
  oktoast: ^3.4.0
  #图片缓存加载框架
  extended_image: ^10.0.1
  # 分栏switch https://pub.dev/packages/toggle_switch
  toggle_switch: ^2.3.0
  # Tab Switch https://pub.dev/packages/animated_toggle_switch
  animated_toggle_switch: ^0.8.5
  #骨架屏闪 https://pub.dev/packages/shimmer_animation/
  shimmer_animation: ^2.2.2
  # Switch  https://pub.dev/packages/toggle_switch
  flutter_switch: ^0.3.2
  #数据json解析（官方框架）https://pub-web:flutter-io:cn/packages/json_annotation:
  json_annotation: ^4.9.0
  #生物识别 指纹或面部 https://pub.dev/packages/local_auth
  local_auth: ^2.3.0
  local_auth_platform_interface: ^1.0.10
  # Dart 官方提供的代码格式化工具 https://pub.dev/packages/dart_style
  dart_style: ^3.1.1
  #扫码 https://pub.dev/packages/google_mlkit_barcode_scanning
  fl_mlkit_scanning: ^5.2.2
  # WebView
  flutter_inappwebview: ^6.1.5
  #分享 https://pub.dev/packages/share_plus
  share_plus: ^11.0.0
  #极光 https://pub.dev/packages/jpush_google_flutter
  jpush_google_flutter: 3.0.4
  # 设备指纹https://pub.dev/packages/fpjs_pro_plugin
  fpjs_pro_plugin: ^4.0.1
  # 网易滑块验证 https://pub.dev/packages/captcha_plugin_flutter/install
  captcha_plugin_flutter: ^1.2.2
  # 通行密钥 https://pub.dev/packages/corbado_auth
  passkeys: ^2.12.0
  corbado_auth: ^3.6.0
  # https://pub.dev/packages/pinput/install 验证码输入框
  pinput: ^5.0.1
  # https://pub.dev/packages/easy_rich_text/install 富文本
  easy_rich_text: ^2.2.0
  # https://pub.dev/packages/flutter_idensic_mobile_sdk_plugin/install KYC
  flutter_idensic_mobile_sdk_plugin: ^1.36.1
  # sumsub 测试用 # 用于HMAC-SHA256签名 测试 sumsub获取Token，后续删除
  crypto: ^3.0.6 
  # 生成随机nonce（可选，也可用其他方式生成）
  uuid: ^4.5.1
  # RSA加密
  pointycastle: ^3.9.1

  
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

    #自动生成多语言代码
  auto_locale_generator: ^0.0.4
  #drift自动生成代码工具
  drift_dev: ^2.28.1
  flutter_lints: ^6.0.0
  #自动生成retrofit代码
  retrofit_generator: ^10.0.2
  build_runner: ^2.4.15
  #自动生成Json model
  json_serializable: ^6.10.0
  analyzer: ^7.4.0


flutter:
  uses-material-design: true


  assets:
    - assets/images/
    - assets/env/
 
  fonts:
    - family: PingFang SC
      fonts:
        - asset: assets/fonts/PingFangSC-Regular.ttf
          weight: 400
        - asset: assets/fonts/PingFangSC-Medium.ttf
          weight: 500
        - asset: assets/fonts/PingFangSC-SemiBold.ttf
          weight: 600
    - family: Manrope
      fonts:
        - asset: assets/fonts/Manrope-Regular.ttf
          weight: 400
        - asset: assets/fonts/Manrope-Medium.ttf
          weight: 500
        - asset: assets/fonts/Manrope-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Manrope-Bold.ttf
          weight: 700

