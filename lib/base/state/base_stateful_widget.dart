/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2024-09-29 11:15:20
 */
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/base/controllers/base_controller.dart';

///具有状态管理的基础页面，满足一些特定需要State的Widget(暂时还未发现需要使用State的场景)
abstract class BaseStatefulWidget<T extends BaseController>
    extends StatefulWidget {
  const BaseStatefulWidget({super.key});

  final String? tag = null;

  T get controller {
    return GetInstance().find<T>(tag: tag);
  }

  ///Get 局部更新字段
  Null get updateId => null;

  ///widget生命周期
  Null get lifecycle => null;

  @override
  State createState() => AutoDisposeState<T>();

  Widget? build(BuildContext context) {
    return null;
  }

  void reassemble() {}

  void initState() {}

  void dispose() {}
}

class AutoDisposeState<T extends GetxController>
    extends State<BaseStatefulWidget>
    with
        AutomaticKeepAliveClientMixin<BaseStatefulWidget>,
        WidgetsBindingObserver {
  // 将 _isAlive 设为私有并提供公共访问器
  bool _isAlive = true;

  // 提供公共的 isAlive getter
  bool get isAlive => _isAlive && mounted;
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<T>(
        tag: widget.tag,
        id: widget.updateId,
        builder: (controller) {
          return widget.build(context)!;
        });
  }

  @override
  void initState() {
    super.initState();
    _isAlive = true;
    widget.initState();
  }

  @override
  void reassemble() {
    super.reassemble();
    widget.reassemble();
  }

  @override
  void dispose() {
    _isAlive = false;
    Get.delete<T>(tag: widget.tag);
    super.dispose();
    widget.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
