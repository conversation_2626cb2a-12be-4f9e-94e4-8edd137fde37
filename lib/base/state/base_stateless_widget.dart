/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-01-03 09:30:01
 * @LastEditTime: 2025-08-04 11:29:29
 */
import 'package:get/get.dart';
import 'package:vcb/base/controllers/base_controller.dart';

///常用页面无状态page封装，基本依赖Controller+OBX实现原有State+StatefulWidget效果
abstract class BaseStatelessWidget<T extends BaseController>
    extends GetView<T> {
  const BaseStatelessWidget({super.key});
}
