/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 11:22:44
 */
// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/widgets/status/app_empty_widget.dart';
import 'package:vcb/widgets/status/app_error_widget.dart';
import 'package:vcb/widgets/status/app_loading_widget.dart';

///包含错误状态，空状态，加载状态的公共组件
abstract class BaseStatusWidget<T extends BaseController> extends GetView<T> {
  const BaseStatusWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return controller.obx((state) => buildContent(context),
        onLoading: const AppLoadingWidget(),
        onError: (error) => AppErrorWidget(onRefresh: () {
              controller.showLoading();
              controller.loadData();
            }),
        onEmpty: const AppEmptyWidget());
  }

  ///showSuccess展示成功的布局
  Widget buildContent(BuildContext context);
}
