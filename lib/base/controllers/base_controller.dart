import 'dart:async';

import 'package:dio/dio.dart';
import 'package:extended_image/extended_image.dart';
import 'package:get/get.dart';
import 'package:vcb/app/app_config.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/http/api_config.dart';
import 'package:vcb/http/dio/Exception/app_except.dart';
import 'package:vcb/utils/log_utils.dart';

///具有状态控制和网络请求能力的controller，等价MVVM中的ViewModel
abstract class BaseController<M> extends SuperController {
  late M api;
  List<StreamSubscription>? _stremSubList;
  CancelToken? cancelToken;

  ///初始化 Controller，例如一些成员属性的初始化
  @override
  void onInit() {
    super.onInit();
    if (AppConfig.instance.enableGetXLog) Log.d('>>>>>>>onInit');
  }

  /// 初始化加载数据
  void loadData();

  /// 发起网络请求，同时处理异常，loading
  void httpRequest<T>(
    Future<T> future,
    FutureOr<dynamic> Function(T value) onValue, {
    Function(Exception e)? error,
    bool showLoading = false, //是否显示Loading
    bool handleError = true, //是否监听Error模式
    bool showToast = true, //是否弹出错误Toast

    bool handleSuccess = true,
  }) {
    if (showLoading) {
      Get.showLoadingDialog();
    }

    // 创建一个新的 CancelToken
    cancelToken = CancelToken();
    future
        .then((t) {
          // 如果 controller 关闭 不返回数据
          if (isClosed) {
            Log.e("Controller is closed, request ignored.");
            return; // 直接返回，不执行后续逻辑
          }

          if (t is BaseResponse) {
            baseResponseHandler(
              t,
              handleSuccess,
              onValue,
              handleError,
              showToast,
            );
          } else {
            if (handleSuccess) {
              showSuccess();
            }

            onValue(t);
          }
        })
        .catchError((e) {
          Log.e(e.toString());

          if (handleError) {
            showError(e: e);
          }
          showToastMessage(e: e, isShowToast: showToast);

          if (error != null) {
            error(e);
          }
        })
        .whenComplete(() {
          if (showLoading) {
            Get.dismissLoadingDialog();
          }
        });
  }

  ///多网络请求简单封装
  Future<void> multiHttpRequest(
    List<Future<dynamic>> futures,
    FutureOr<dynamic> Function(dynamic value) onValue, {
    Function(Exception e)? error,
    CancelToken? cancelToken,
    bool showLoading = false,
    bool handleError = true,
    bool showToast = true,
    bool handleSuccess = true,
  }) async {
    if (showLoading) {
      Get.showLoadingDialog();
    }
    // 创建一个新的 CancelToken
    cancelToken = cancelToken ?? CancelToken();
    Future.wait(futures)
        .then((value) {
          // 如果 controller 关闭 不返回数据
          if (isClosed) {
            Log.e("Controller is closed, request ignored.");
            return; // 直接返回，不执行后续逻辑
          }
          onValue(value);
        })
        .catchError((e) {
          Log.e(e.toString());
          if (handleError) {
            showError(e: e);
          }
          showToastMessage(e: e, isShowToast: showToast);

          if (error != null) {
            error(e);
          }
        })
        .whenComplete(() {
          if (showLoading) {
            Get.dismissLoadingDialog();
          }
        });
  }

  void baseResponseHandler<T>(
    t,
    bool handleSuccess,
    FutureOr<dynamic> Function(T value) onValue,
    bool handleError,
    bool isShowToast,
  ) {
    if (APIConstant.responseCode == t.code) {
      if (handleSuccess) {
        showSuccess();
      }

      onValue(t);
    } else {
      if (handleError) {
        showToastMessage(errorMessage: t.data, isShowToast: isShowToast);
        showError(errorMessage: t.data);
      } else {
        showToastMessage(errorMessage: t.data, isShowToast: isShowToast);
        onValue(t);
        if (handleSuccess) {
          showSuccess();
        }
      }
    }
  }

  void baseResponseHandlerV2<T>(
    t,
    bool handleSuccess,
    FutureOr<dynamic> Function(T value) onValue,
    bool handleError,
    bool isShowToast,
  ) {
    if (APIConstant.responseCodeV2 == t.code) {
      if (handleSuccess) {
        showSuccess();
      }

      onValue(t);
    } else {
      if (handleError) {
        showToastMessage(errorMessage: t.message, isShowToast: isShowToast);
        showError(errorMessage: t.message);
      } else {
        showToastMessage(errorMessage: t.data, isShowToast: isShowToast);
        onValue(t);
        if (handleSuccess) {
          showSuccess();
        }
      }
    }
  }

  @override
  void onDetached() {
    if (AppConfig.instance.enableGetXLog) Log.d('>>>>>>>onDetached');
  }

  @override
  void onInactive() {
    if (AppConfig.instance.enableGetXLog) Log.d('>>>>>>>onInactive');
  }

  ///或前台返回后台
  @override
  void onPaused() {
    if (AppConfig.instance.enableGetXLog) Log.d('>>>>>>>onPaused');
  }

  ///或后台返回前台回调
  @override
  void onResumed() {
    if (AppConfig.instance.enableGetXLog) Log.d('>>>>>>>onResumed');
  }

  @override
  void onHidden() {
    if (AppConfig.instance.enableGetXLog) Log.d('>>>>>>>onHidden');
  }

  ///就绪后的业务处理，如异步操作、导航进入的参数处理等；
  @override
  void onReady() {
    super.onReady();
    if (AppConfig.instance.enableGetXLog) Log.d('>>>>>>>onReady');
    try {
      api = Get.find<M>();
    } catch (e) {
      //print e
    }
  }

  ///释放资源，避免内存泄露，同时也可以进行数据持久化
  @override
  void onClose() {
    super.onClose();
    if (AppConfig.instance.enableGetXLog) Log.d('>>>>>>>onClose');
  }

  void showSuccess() {
    change(null, status: RxStatus.success());
  }

  void showEmpty() {
    change(null, status: RxStatus.empty());
  }

  void showError({String? errorMessage, dynamic e}) {
    String msg = "";
    if (e != null) {
      if (e is AppException) {
        msg = e.msg!;
        change(null, status: RxStatus.error(msg));
      } else {
        msg = e.toString();
        change(null, status: RxStatus.error(msg));
      }
    } else {
      change(null, status: RxStatus.error(errorMessage ?? ""));
    }
  }

  void showToastMessage({
    String? errorMessage,
    dynamic e,
    bool isShowToast = false,
  }) {
    if (!isShowToast) return;
    String? msg;
    if (e != null) {
      if (e is AppException) {
        ///这种异常由于接口问题过多 不让弹出Toast
        if (e.errortype == DioExceptionType.badResponse) {
          msg = null;
        } else {
          msg = e.msg;
        }

        change(null, status: RxStatus.error(msg));
      } else {
        msg = e.toString();
        change(null, status: RxStatus.error(msg));
      }
      if (!GetUtils.isNull(msg)) {
        Get.showToast(msg!);
      }
    } else {
      change(null, status: RxStatus.error(errorMessage ?? ""));
      if (!GetUtils.isNull(errorMessage)) {
        Get.showToast(errorMessage);
      }
    }
  }

  void showLoading() {
    change(null, status: RxStatus.loading());
  }

  ///是否使用GetX查找EventBus
  bool useEventBus() => false;

  ///管理Eventbus解订阅
  void addStremSub(StreamSubscription? streamSubscription) {
    _stremSubList ??= [];
    if (streamSubscription != null) {
      _stremSubList?.add(streamSubscription);
    }
  }

  /// 取消当前请求
  void cancelRequest() {
    cancelToken?.cancel("Request canceled");
    Log.e("Current request has been canceled.");
  }
}
