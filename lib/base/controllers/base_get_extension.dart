/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-15 12:59:21
 * @LastEditTime: 2025-08-04 13:04:02
 */
import 'package:common_utils/common_utils.dart';
import 'package:corbado_auth/corbado_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vcb/app/app_controller.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/database/db_provider.dart';
import 'package:vcb/database/storage/storage_provider.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/route/routes.dart';
import 'package:vcb/utils/log_utils.dart';
import 'package:vcb/utils/permissions_service.dart';
import 'package:vcb/widgets/dialog/alert_dialog.dart';
import 'package:vcb/widgets/dialog/bottom_sheet_dialog.dart';
import 'package:vcb/widgets/toast/toast.dart';

/// @description :getx 扩展函数
extension GetExtension on GetInterface {
  // 全局AppController
  AppController get appController => Get.find<AppController>();

  // 全局PermissionsService
  PermissionsService get permissionsService => Get.find<PermissionsService>();

  /// 数据库
  AppDatabase get database => Get.find<AppDatabase>();

  // 全局CorbadoAuth
  CorbadoAuth get corbadoAuth => Get.find<CorbadoAuth>();

  /// 首次启动
  bool isFirstLaunch() {
    return StorageManager.getValue(key: StorageKey.fristLaunch) ?? true;
  }

  //String判断是否空或者空字符
  bool isEmptyString(String? str) {
    if (str == null) return true;
    bool result = ObjectUtil.isEmptyString(str);
    if (!result) {
      if (str == 'null') return true;
    }
    return result;
  }

  //Returns true  String or List or Map is empty.
  bool isEmpty(Object? object) {
    return ObjectUtil.isEmpty(object);
  }

  //底部Button安全距离
  double getSafetyBottomPadding() {
    return Get.mediaQuery.padding.bottom > 0
        ? Get.mediaQuery.padding.bottom
        : Get.setHeight(16);
  }

  //适配大小屏幕 等宽图片
  double setImageSize(double size) {
    return size.h;
  }

  //适配大小屏幕 Padding
  double setPaddingSize(double size) {
    return size.h;
  }

  //适配大小屏幕 宽度
  double setWidth(double width) {
    return width.w;
  }

  //适配大小屏幕 高度
  double setHeight(double height) {
    return height.h;
  }

  //适配大小屏幕 弧度
  double setRadius(double radius) {
    return radius.r;
  }

  //适配大小屏幕 字体
  double setFontSize(double fontSize) {
    return fontSize.sp;
  }

  //是否平板
  bool isTablet() {
    bool isTab = false;
    var shortestSide = Get.size.shortestSide;
    if (shortestSide > 600) {
      isTab = true;
    } else {
      isTab = false;
    }
    return isTab;
  }

  //设置字体
  String setFontFamily() {
    return AppFonts.fontFamilyPingFang;
  }

  //设置纯数字英文字体
  String setNumberFontFamily() {
    return AppFonts.fontFamilyManrope;
  }

  void showToast(
    String? msg, {
    bool isShortToast = true,
    ToastMode toastMode = ToastMode.normal,
  }) {
    if (GetUtils.isNull(msg)) return;
    showToastMessage(msg, isShortToast: isShortToast, toastMode: toastMode);
  }

  void showSnackbar(
    String? msg, {
    String? title,
    int? seconds,
    SnackPosition? snackPosition,
  }) {
    if (GetUtils.isNull(msg)) return;
    showSnackbarMessage(
      msg!,
      title: title!,
      seconds: seconds!,
      snackPosition: snackPosition,
    );
  }

  ///隐藏dialog
  void dismissDialog() {
    if (Get.isDialogOpen != null && Get.isDialogOpen!) {
      Get.back();
    }
  }

  String getEnvByKey(String key) {
    try {
      return dotenv.env[key] ?? "";
    } catch (e) {
      Log.e('getEnvByKey error: $e');
      return "";
    }
  }

  ///复制
  void copy(String? text) {
    if (ObjectUtil.isEmpty(text)) return;
    Clipboard.setData(ClipboardData(text: text!)).then(
      (result) =>
          showToast(ID.stringCopySuccess.tr, toastMode: ToastMode.success),
    );
  }

  ///跳转扫码
  Future<String> toScanner({dynamic arguments}) async {
    if (await permissionsService.requestCameraPermission()) {
      final result = await Get.toNamed(
        AppRoutes.scanPage,
        arguments: arguments,
      )!;
      if (!Get.isEmptyString(result)) {
        return result;
      }
    }
    return "";
  }

  ///跳转App Web浏览器
  void toWeb({required String? url, String? title, bool showTitle = true}) {
    Get.toNamed(
      AppRoutes.webPage,
      arguments: {
        GetArgumentsKey.url: url,
        GetArgumentsKey.title: title,
        GetArgumentsKey.showTitle: showTitle,
      },
    );
  }

  void share(String? text) {
    if (ObjectUtil.isEmpty(text)) return;
    ShareParams params = ShareParams(text: text!);
    SharePlus.instance.share(params);
  }

  ///跳转外部浏览器
  Future<void> openLink(String? url, {Function? onError}) async {
    if (GetUtils.isNull(url)) {
      return;
    }
    final Uri uri = Uri.parse(url!);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (onError != null) {
        await onError();
      } else {
        Get.showToast(ID.dataError.tr);
      }
    }
  }

  Future<void> makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    await launchUrl(launchUri);
  }

  ///全局加载弹窗
  void showLoadingDialog({String? loadingText}) {
    EasyLoading.show(status: loadingText ?? ID.loading.tr);
  }

  ///关闭全局加载弹窗
  void dismissLoadingDialog() {
    EasyLoading.dismiss();
  }

  ///中间Alertdialog
  Future<void> showAlertDialog({
    String? title,
    String? content,
    bool barrierDismissible = true, //点击外部关闭
    String? onConfirmText,
    String? onCancelText,
    VoidCallback? onConfirm,
    bool disableBack = true, // 禁用Android back键，默认不禁用
    VoidCallback? onBackPressed, //Android back键 事件
    VoidCallback? onCancel,
    VoidCallback? onContinue,
    Widget? child,
  }) async {
    if (Get.isDialogOpen != null && Get.isDialogOpen!) {
      Get.back();
    }
    await Get.dialog(
      barrierDismissible: barrierDismissible,
      PopScope(
        canPop: disableBack,
        onPopInvokedWithResult: (didPop, result) async {
          if (result != null) {
            return; // 如果 result 不为空，直接返回
          }
          if (didPop) {
            return;
          }

          if (onBackPressed != null) {
            onBackPressed();
          }
        },
        child:
            child ??
            AlertDialog(
              title: title,
              content: content,
              onConfirmText: onConfirmText,
              onCancelText: onCancelText,
              onContinue: onContinue,
              onConfirm: onConfirm,
              onCancel: onCancel,
            ),
      ),
    );
  }

  ///底部dialog
  Future<void> showBottomSheet({
    String? title,
    bool barrierDismissible = true, //点击外部关闭
    bool isScrollControlled = true, //设置为false，BottomSheet将根据其内容的大小来决定其高度
    bool enableDrag = true, // 是否可以通过拖动BottomSheet来关闭它
    bool hideHeader = false, // 隐藏Header
    bool disableBack = true, // 禁用Android back键，默认不禁用
    Widget? bodyWidget, // 自适应高度的body
    Widget? fullScreenBodyWidget, // 高度充满屏幕的 body
    Widget? bottomWidget, // 底部区域 如底部Buttonn
    VoidCallback? onBackPressed, //Android back键 事件
    VoidCallback? onCancel, //右上角关闭对话框
    double? paddingBottom, //自定义距底部高度 如果底部框是个列表，这里设置 为0
    Widget? customHeadWidget, //自定义顶部
    EdgeInsetsGeometry? padding,
    Color? bgColor,
    Function()? onBack, // 监听关闭回调
  }) async {
    await Get.bottomSheet(
      BottomSheetDialog(
        title: title,
        onBackPressed: onBackPressed,
        onCancel: onCancel,
        bgColor: bgColor,
        bodyWidget: bodyWidget,
        fullScreenBodyWidget: fullScreenBodyWidget,
        disableBack: disableBack,
        hideHeader: hideHeader,
        bottomWidget: bottomWidget,
        paddingBottom: paddingBottom,
        customHeadWidget: customHeadWidget,
        padding: padding,
      ),

      backgroundColor: bgColor ?? Get.theme.bgColor,
      ignoreSafeArea: false, //是否忽略安全区域（如iPhone X的刘海屏和底部指示条
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
    ).then((value) => onBack != null ? onBack() : {});
  }
}
