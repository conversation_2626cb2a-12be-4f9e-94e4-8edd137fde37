/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-16 15:21:26
 * @LastEditTime: 2024-09-11 11:05:56
 */

import 'package:intl/intl.dart';

class DateHeleper {
  static const String format = 'yyyy-MM-dd HH:mm:ss';

  /// 获取手机当前时间
  static String getCurrentTimeFormatted() {
    DateTime now = DateTime.now();
    String formattedDate = DateFormat(format).format(now);
    return formattedDate;
  }

  /// 将时间戳转换为指定格式的字符串
  static String formatTimestamp(int? timestamp, {String format = format}) {
    if (timestamp == 0) return '';
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp!);
    return DateFormat(format).format(dateTime);
  }

  /// 将当前DateTime转换为时间戳
  static int getCurrentTimestamp() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  /// 将当前DateTime转换为时间戳
  static int getCurrentTimeSecond() {
    return DateTime.now().millisecondsSinceEpoch ~/ 1000;
  }

  /// 将时间戳转换为DateTime对象
  static DateTime convertTimestampToDateTime(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }

  /// 将DateTime转换为时间戳
  static int convertDateTimeToTimestamp(DateTime dateTime) {
    return dateTime.millisecondsSinceEpoch;
  }

  /// 格式化DateTime为指定格式的字符串
  static String formatDateTime(DateTime dateTime,
      {String format = 'yyyy-MM-dd – kk:mm'}) {
    return DateFormat(format).format(dateTime);
  }

  /// 将ISO8601字符串转换为DateTime对象
  static DateTime parseIso8601String(String iso8601String) {
    return DateTime.parse(iso8601String);
  }

  /// 获取特定日期的开始时间戳
  static int getTimestampAtStartOfDay(DateTime dateTime) {
    DateTime startOfDay = DateTime(dateTime.year, dateTime.month, dateTime.day);
    return startOfDay.millisecondsSinceEpoch;
  }
}
