/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-16 15:21:26
 * @LastEditTime: 2024-03-18 16:06:02
 */

import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';

class AddressUtils {
  static String laterOmitAddress(String? address, {int len = 4}) {
    if (Get.isEmptyString(address)) return "";
    if (address!.length > (len * 2)) {
      return '${address.substring(0, len * 2)}...';
    }
    return address;
  }

  static String omitAddress(String? address, {int len = 6}) {
    if (Get.isEmptyString(address)) return "";
    if (address!.length > (len * 2)) {
      return '${address.substring(0, len)}...${address.substring(address.length - len, address.length)}';
    }
    return address;
  }

  static String hideOmitAddress(String address, {int len = 4}) {
    if (address.isEmpty) return "";
    if (address.length > (len * 2)) {
      return '${address.substring(0, len)} **** **** ${address.substring(address.length - len, address.length)}';
    }
    return address;
  }
}
