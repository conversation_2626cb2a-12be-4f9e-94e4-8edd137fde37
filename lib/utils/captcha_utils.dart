/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-07 17:16:05
 */
import 'package:captcha_plugin_flutter/captcha_plugin_flutter.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/utils/log_utils.dart';

class CaptchaUtils {
  static void showCaptcha() {
    _captchaPlugin().showCaptcha(
      onSuccess: (value) {
        Log.logPrint(value);
      },
    );
  }

  static CaptchaPluginFlutter _captchaPlugin() {
    final CaptchaPluginFlutter captchaPlugin = CaptchaPluginFlutter();
    CaptchaPluginConfig styleConfig = CaptchaPluginConfig(
        radius: 10,
        capBarTextColor: "#25D4D0",
        capBarTextSize: 18,
        capBarTextWeight: "bold",
        borderColor: "#25D4D0",
        borderRadius: 10,
        backgroundMoving: "#DC143C",
        executeBorderRadius: 10,
        executeBackground: "#DC143C",
        capBarTextAlign: "left",
        capPaddingTop: 10,
        capPaddingRight: 10,
        capPaddingBottom: 10,
        capPaddingLeft: 10,
        paddingTop: 15,
        paddingBottom: 15);
    String captchaId = Get.getEnvByKey(SDKConstant.CaptchaId);
    captchaPlugin.init({
      "captcha_id": captchaId,
      "is_debug": true,
      "dimAmount": 0.8,
      "is_touch_outside_disappear": true,
      "timeout": 8000,
      "is_hide_close_button": false,
      "use_default_fallback": true,
      "failed_max_retry_count": 4,
      "is_close_button_bottom": true,
      'theme': 'light',
      "lang": 'NTESVerifyCodeLangCN',
      "styleConfig": styleConfig.toJson(),
    });
    return captchaPlugin;
  }
}
