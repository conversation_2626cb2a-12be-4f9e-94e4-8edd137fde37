/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2025-03-10 10:42:03
 */
import 'package:flutter/cupertino.dart';

/// @description :软键盘相关工具类
class KeyboardUtils {
  ///隐藏软键盘
  ///[context] 上下文
  static void hideKeyboard(BuildContext context) {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  ///无上下文隐藏软键盘
  ///[context] 无上下文
  static void hideKeyboardNoContext() {
    FocusManager.instance.primaryFocus!.unfocus(); //系统键盘隐藏
  }
}
