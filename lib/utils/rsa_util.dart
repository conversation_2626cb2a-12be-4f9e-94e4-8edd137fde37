/*
 * @author: Chen<PERSON>
 * @description: RSA加密工具类
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'dart:convert';
import 'dart:typed_data';
import 'package:pointycastle/asn1/asn1_parser.dart';
import 'package:pointycastle/asn1/primitives/asn1_integer.dart';
import 'package:pointycastle/asn1/primitives/asn1_sequence.dart';
import 'package:pointycastle/export.dart';
import 'package:vcb/utils/log_utils.dart';

class RSAUtil {
  static RSAPublicKey? _publicKey;

  /// 初始化RSA公钥
  static void init(String publicKeyPem) {
    try {
      _publicKey = parsePublicKeyFromPem(publicKeyPem);
      Log.logPrint("RSA公钥初始化成功");
    } catch (e) {
      Log.e("RSA公钥初始化失败: $e");
      rethrow;
    }
  }

  /// 从PEM格式解析RSA公钥
  static RSAPublicKey parsePublicKeyFromPem(String pemString) {
    try {
      // 移除PEM头尾和换行符
      final rows = pemString
          .split('\n')
          .where((row) => !row.startsWith('-----'))
          .join('');

      final bytes = base64Decode(rows);
      final asn1Parser = ASN1Parser(bytes);
      final topLevelSeq = asn1Parser.nextObject() as ASN1Sequence;

      // 解析模数和指数
      final modulus = (topLevelSeq.elements![0] as ASN1Integer).integer;
      final exponent = (topLevelSeq.elements![1] as ASN1Integer).integer;

      return RSAPublicKey(modulus!, exponent!);
    } catch (e) {
      Log.e("解析RSA公钥失败: $e");
      rethrow;
    }
  }

  /// RSA加密
  static String encrypt(String plainText) {
    if (_publicKey == null) {
      throw Exception("RSA公钥未初始化，请先调用 RSAUtil.init()");
    }

    try {
      final encryptor = OAEPEncoding(RSAEngine())
        ..init(true, PublicKeyParameter<RSAPublicKey>(_publicKey!));

      final data = Uint8List.fromList(utf8.encode(plainText));
      final encrypted = encryptor.process(data);

      final result = base64Encode(encrypted);
      Log.logPrint("RSA加密成功，原文长度: ${plainText.length}，密文长度: ${result.length}");

      return result;
    } catch (e) {
      Log.e("RSA加密失败: $e");
      rethrow;
    }
  }

  /// 检查RSA是否已初始化
  static bool get isInitialized => _publicKey != null;

  /// 获取公钥信息（用于调试）
  static String get publicKeyInfo {
    if (_publicKey == null) return "未初始化";
    return "模数长度: ${_publicKey!.modulus!.bitLength} bits";
  }
}
