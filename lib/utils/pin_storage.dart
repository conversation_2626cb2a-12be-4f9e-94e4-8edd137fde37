/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 11:04:34
 */
import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:vcb/utils/aes_helper.dart';

class PinStorage {
  final storage = FlutterSecureStorage();

// 生成随机盐
  String generateSalt([int length = 16]) {
    final random = Random.secure();
    final salt = List<int>.generate(length, (_) => random.nextInt(256));
    return base64.encode(salt);
  }

// 计算哈希（使用 SHA-256）
  String hashPIN(String pin, String salt) {
    final bytes = utf8.encode(pin + salt);
    return sha256.convert(bytes).toString();
  }

// 存储 PIN 哈希 + 盐
  Future<void> saveHashedPIN(String pin) async {
    final salt = generateSalt();
    final hashedPin = hashPIN(pin, salt);

    String saltAesData =
        AesHelpler(EncryptKey.aesMainKey, EncryptKey.aesIVKey).encrypt(salt);

    String hashedPinAesData =
        AesHelpler(EncryptKey.aesMainKey, EncryptKey.aesIVKey)
            .encrypt(hashedPin);
    await storage.write(key: 'pin_hash', value: hashedPinAesData);
    await storage.write(key: 'pin_salt', value: saltAesData);
  }

// 校验 PIN
  Future<bool> verifyPIN(String inputPIN) async {
    final storedHashAes = await storage.read(key: 'pin_hash');
    final storedSaltAes = await storage.read(key: 'pin_salt');
    if (storedHashAes == null || storedSaltAes == null) return false;
    String storedSalt = AesHelpler(EncryptKey.aesMainKey, EncryptKey.aesIVKey)
        .decrypt(storedSaltAes);

    String storedHash = AesHelpler(EncryptKey.aesMainKey, EncryptKey.aesIVKey)
        .decrypt(storedHashAes);

    return hashPIN(inputPIN, storedSalt) == storedHash;
  }

  // 删除 PIN
  Future clearPIN() async {
    // 删除 PIN 相关数据
    await storage.delete(key: 'pin_hash');
    await storage.delete(key: 'pin_salt');
  }
}

class EncryptKey {
  static String get aesMainKey => dotenv.env['AES_MAIN_KEY'] ?? '';
  static String get aesIVKey => dotenv.env['AES_IV_KEY'] ?? '';
  static String get keyUltraOrP3Plus => dotenv.env['KEY_ULTRA_OR_P3PLUS'] ?? '';
  static String get keyPro3 => dotenv.env['KEY_PRO3'] ?? '';
  static String get keyUploadUserData =>
      dotenv.env['KEY_UPLOAD_USER_DATA'] ?? '';
}
