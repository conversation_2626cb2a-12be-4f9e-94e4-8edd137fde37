import 'package:common_utils/common_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class Log {
  static RxList<DebugLogModel> debugLogs = <DebugLogModel>[].obs;

  static void addDebugLog(String content, Color? color) {
    if (kReleaseMode) {
      return;
    }
    if (content.contains("请求响应")) {
      content = content.split("\n").join('\n💡 ');
    }
    try {
      debugLogs.insert(0, DebugLogModel(DateTime.now(), content, color: color));
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  static Logger logger = Logger(
    output: CustomLogOutput(lineLength: 1000),
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 1000,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.dateAndTime,
    ),
  );

  static void d(String message) {
    addDebugLog(message, Colors.blue);
    logger.d("${DateTime.now().toString()}\n$message");
  }

  ///打印长Log,
  static void s(dynamic message, {String? tag}) {
    if (GetUtils.isNull(message)) return;
    if (kReleaseMode) {
      return;
    }
    LogUtil.v(
        tag: tag ?? "-------------------Start------------------",
        "${DateTime.now().toString()}\n${message.toString()}\n${"--------------------End----------------"}");
  }

  ///打印长Log,蓝色
  static void r(dynamic message,
      {String? tag = "", String? start = "", String? end = ""}) {
    if (GetUtils.isNull(message)) return;
    if (kReleaseMode) {
      return;
    }

    LogUtil.d(tag: tag ?? "$start", "\n${message.toString()}\n${"$end"}");
  }

  static void i(String message) {
    addDebugLog(message, Colors.orange);
    logger.i("${DateTime.now().toString()}\n$message");
  }

  static void e(String message, {StackTrace? stackTrace}) {
    addDebugLog('$message\r\n\r\n$stackTrace', Colors.red);
    logger.e("${DateTime.now().toString()}\n$message", stackTrace: stackTrace);
  }

  static void w(String message) {
    addDebugLog(message, Colors.pink);
    logger.w("${DateTime.now().toString()}\n$message");
  }

  static void logPrint(dynamic obj) {
    addDebugLog(obj.toString(), Colors.red);
    if (obj is Error) {
      Log.e(obj.toString(), stackTrace: obj.stackTrace ?? StackTrace.current);
    } else if (kDebugMode) {
      print(obj);
    }
  }

  static void g(String message) {
    // 使用ANSI转义序列让debugPrint显示绿色
    debugPrint('\x1B[32m$message\x1B[0m');
  }

  static void p(String message) {
    debugPrint(message);
  }

  /// 测试打印Toast信息-显示红色
  static void t(String message) {
    debugPrint('\x1B[31m$message\x1B[0m');
  }
}

class DebugLogModel {
  final String content;
  final DateTime datetime;
  final Color? color;
  DebugLogModel(this.datetime, this.content, {this.color});
}

class CustomLogOutput extends LogOutput {
  final int lineLength;

  CustomLogOutput({this.lineLength = 500});

  @override
  void output(OutputEvent event) {
    for (var line in event.lines) {
      if (line.length <= lineLength) {
        if (kDebugMode) {
          print(line);
        }
      } else {
        // 如果日志行超长，则分割并打印
        var start = 0;
        while (start < line.length) {
          var end = start + lineLength;
          if (end > line.length) {
            end = line.length;
          }
          if (kDebugMode) {
            print(line.substring(start, end));
          }
          start += lineLength;
        }
      }
    }
  }
}
