/*
 * @description: 权限服务
 * @Author: wangdognshenng
 * @Date: 2024-01-18 17:15:39
 * @LastEditTime: 2025-08-04 10:53:05
 */
import 'dart:io';

import 'package:permission_handler/permission_handler.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/database/storage/storage_provider.dart';
import 'package:vcb/res/resource.dart';

class PermissionsService {
  /// 请求单个权限
  Future<bool> requestPermission(Permission permission) async {
    var status = await permission.status;
    if (status.isGranted) {
      // 已经授权
      return true;
    } else {
      status = await permission.request();
      return status.isGranted;
    }
  }

  /// 请求多个权限
  Future<bool> requestMultiplePermissions(List<Permission> permissions) async {
    Map<Permission, PermissionStatus> statuses = await permissions.request();
    return statuses.values.every((status) => status.isGranted);
  }

  /// 检查权限状态
  Future<bool> hasPermission(Permission permission) async {
    var status = await permission.status;
    return status.isGranted;
  }

  /// 申请相机权限
  Future<bool> requestCameraPermission() async {
    var status = await Permission.camera.status;
    if (status.isGranted) {
      // 相机权限已经被授予

      return true;
    } else if (status.isDenied) {
      // 相机权限被拒绝，请求权限

      status = await Permission.camera.request();
      if (status.isGranted) {
        // 权限请求之后被授予
        return true;
      }
      openAppSettings();

      Get.showToast(ID.cameraAccessNotEnabled.tr);

      // 权限请求之后依然被拒绝
      return false;
    } else if (status.isPermanentlyDenied) {
      // 权限被永久拒绝，需要用户手动开启
      Get.showAlertDialog(
          title: ID.stringTips.tr,
          content: ID.cameraAccessNotEnabled.tr,
          onConfirmText: ID.gotoSettingTitle.tr,
          onConfirm: () async {
            openAppSettings();
            Get.back();
          });
      return false;
    }
    return false;
  }

  ///兼容ios13.x版本 申请存本地存储权限
  Future<bool> requestStoragePermission({bool isOpenSetting = false}) async {
    bool isGranted = false;
    try {
      Permission permission = Permission.storage;
      if (Platform.isIOS) {
        permission = Permission.photos;
      }
      var status = await permission.status;
      if (status.isGranted) {
        isGranted = true;
      } else if (status.isLimited) {
        ///ios选择选中的图片时也给予权限
        isGranted = true;
      } else if (status.isDenied) {
        /// 第一次申请被拒绝再次请求
        isGranted = false;
        PermissionStatus status = await permission.request();
        if (status.isPermanentlyDenied) {
          if (isOpenSetting) {
            bool userHaveDenied =
                StorageManager.getValue(key: GetArgumentsKey.kHaveDenied);
            if (userHaveDenied) {
              await openAppSettings();
              return false;
            } else {
              StorageManager.saveValue(
                  key: GetArgumentsKey.kHaveDenied, value: true);
              Get.showToast(ID.storageAccessNotEnabled.tr);
            }
          } else {
            Get.showToast(ID.storageAccessNotEnabled.tr);
          }
        } else {
          if (status.isGranted || status.isLimited) {
            isGranted = true;
          }
        }
      } else {
        isGranted = false;
        await openAppSettings();
      }
      return isGranted;
    } catch (e) {
      return false;
    }
  }
}
