/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-16 15:21:26
 * @LastEditTime: 2025-08-04 10:38:47
 */

import 'dart:convert';

import 'package:regexed_validator/regexed_validator.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/res/resource.dart';

class FileUtils {
  static Future<void> launchAppStore(String url) async {
    if (Get.isEmptyString(url)) {
      url = AgreementUrl.official;
    }
    Get.openLink(url);
  }

  static String deCodeBase64(String base64Str) {
    List<int> decodedData = base64Decode(base64Str);
    return utf8.decode(decodedData);
  }

  static String formatUrl(String url, {bool addHttpHeader = false}) {
    if (url.isEmpty) {
      return "";
    }
    String lowerUrl = url.toLowerCase();
    if (!lowerUrl.startsWith('https://') && !lowerUrl.startsWith('http://')) {
      // 只有当 URL 没有以 "https://" 开头时才加上
      if (validator.url(url) || addHttpHeader) {
        return 'https://$url'; // 使用 HTTPS 前缀
      }
    } else if (validator.url(url)) {
      return url;
    }

    return ""; // 如果 URL 无效，返回空字符串
  }

  /// Checks if the given URL is an HTTP URL.
  static bool isHttpUrl(String? url) {
    return url != null &&
        url.length > 6 &&
        url.substring(0, 7).toLowerCase() == "http://";
  }

  /// Checks if the given URL is an HTTPS URL.
  static bool isHttpsUrl(String? url) {
    return url != null &&
        url.length > 7 &&
        url.substring(0, 8).toLowerCase() == "https://";
  }

  static String getUrlfileName(String? url) {
    Uri uri = Uri.parse(url!);

    return uri.pathSegments.last;
  }

  /// Checks if the given URL is an HTTPS URL.
  static bool isNum(String value) {
    return GetUtils.isNum(value);
  }
}
