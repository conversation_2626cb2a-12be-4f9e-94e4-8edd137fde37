import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vcb/app/app_config.dart';
import 'package:vcb/main.dart';

/// 切换为mock数据环境入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  AppConfig.appConfigType = AppConfigType.mock;
  AppConfig.instance.setApiConfig();
  // 复用主入口的启动流程
  await initAppService();
  runApp(const MyApp());
}
