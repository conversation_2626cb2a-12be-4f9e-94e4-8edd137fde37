/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 14:06:05
 */
abstract class AppRoutes {
  /// main
  static const mainPage = '/main_page';

  /// 资产
  static const assetsPage = '/assets_page';

  /// 理财
  static const financePage = '/finance_page';

  /// 借贷
  static const loanPage = '/loan_page';

  /// 个人中心
  static const profilePage = '/profile_page';

  /// auth
  static const authPage = "/auth_page";
  static const loginPage = '/login_page';
  static const registerPage = '/register_page';

  /// Example 模块
  static const examplePage = '/example_page';
  static const passkeyExamplePage = '/passkey_example_page';
  static const gptPasskeyPage = '/gpt_passkey_page';
  static const uiComponentsExamplePage = '/ui_components_example_page';
  static const deviceFingerprintExamplePage =
      '/device_fingerprint_example_page';
  static const neteaseSliderExamplePage = '/netease_slider_example_page';
  static const kycExamplePage = '/kyc_example_page';

  /// 扫描
  static const scanPage = '/scan_page';

  /// 扫码错误结果页面
  static const scanResultPage = "/scan_reselt_page";

//App内置浏览器
  static const webPage = "/web_page";
}
