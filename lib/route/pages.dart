import 'package:get/get_navigation/src/routes/get_route.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';
import 'package:vcb/main/main_controller.dart';
import 'package:vcb/main/main_page.dart';
import 'package:vcb/modules/assets/assets_controller.dart';
import 'package:vcb/modules/assets/assets_page.dart';
import 'package:vcb/modules/auth/auth_page.dart';
import 'package:vcb/modules/auth/login/login_controller.dart';
import 'package:vcb/modules/auth/login/login_page.dart';
import 'package:vcb/modules/auth/register/register_controller.dart';
import 'package:vcb/modules/auth/register/register_page.dart';
import 'package:vcb/modules/earn/earn_controller.dart';
import 'package:vcb/modules/earn/earn_page.dart';
import 'package:vcb/modules/example/controllers/device_fingerprint_controller.dart';
import 'package:vcb/modules/example/controllers/gpt_passkey_controller.dart';
import 'package:vcb/modules/example/controllers/passkey_auth_controller.dart';
import 'package:vcb/modules/example/example_controller.dart';
import 'package:vcb/modules/example/example_page.dart';
import 'package:vcb/modules/example/examples/device_fingerprint_example.dart';
import 'package:vcb/modules/example/examples/gpt_passkey_page.dart';
import 'package:vcb/modules/example/examples/kyc_example.dart';
import 'package:vcb/modules/example/examples/netease_slider_example.dart';
import 'package:vcb/modules/example/examples/passkey_example.dart';
import 'package:vcb/modules/example/examples/ui_components_example.dart';
import 'package:vcb/modules/loan/loan_controller.dart';
import 'package:vcb/modules/loan/loan_page.dart';
import 'package:vcb/modules/profile/profile_controller.dart';
import 'package:vcb/modules/profile/profile_page.dart';
import 'package:vcb/modules/scan/pages/scan_result_page.dart';
import 'package:vcb/modules/scan/scan_controller.dart';
import 'package:vcb/modules/scan/scan_page.dart';
import 'package:vcb/modules/web/web_controller.dart';
import 'package:vcb/modules/web/web_page.dart';
import 'package:vcb/route/routes.dart';

final routerPages = <GetPage>[
  ///---------------------------- main------------------------------

  ///主入口
  GetPage(
      name: AppRoutes.mainPage,
      page: () => const MainPage(),
      transition: Transition.noTransition,
      binding: MainBinding()),

  ///资产
  GetPage(
    name: AppRoutes.assetsPage,
    page: () => const AssetsPage(),
    binding: AssetsBinding(),
  ),

  //理财
  GetPage(
    name: AppRoutes.financePage,
    page: () => const EarnPage(),
    binding: EarnBinding(),
  ),

  ///借贷
  GetPage(
    name: AppRoutes.loanPage,
    page: () => const LoanPage(),
    binding: LoanBinding(),
  ),

  ///个人中心
  GetPage(
    name: AppRoutes.profilePage,
    page: () => const ProfilePage(),
    binding: ProfileBinding(),
  ),

  ///---------------------------- auth------------------------------
  /// Auth
  GetPage(
    name: AppRoutes.authPage,
    page: () => const AuthPage(),
    transition: Transition.downToUp,
  ),

  /// 登录
  GetPage(
    name: AppRoutes.loginPage,
    page: () => const LoginPage(),
    transition: Transition.downToUp,
    binding: LoginBinding(),
  ),

  /// 注册
  GetPage(
    name: AppRoutes.registerPage,
    page: () => const RegisterPage(),
    binding: RegisterBinding(),
  ),

  ///---------------------------- example------------------------------

  /// Example 中心
  GetPage(
    name: AppRoutes.examplePage,
    page: () => const ExamplePage(),
    binding: ExampleBinding(),
  ),

  /// Passkey 示例
  GetPage(
    name: AppRoutes.passkeyExamplePage,
    page: () => const PasskeyExamplePage(),
    binding: PasskeyAuthBinding(),
  ),

  /// GPT Passkey 方案测试
  GetPage(
    name: AppRoutes.gptPasskeyPage,
    page: () => const GPTPasskeyPage(),
    binding: GPTPasskeyBinding(),
  ),

  /// UI 组件示例
  GetPage(
    name: AppRoutes.uiComponentsExamplePage,
    page: () => const UIComponentsExamplePage(),
  ),

  /// 设备指纹示例
  GetPage(
    name: AppRoutes.deviceFingerprintExamplePage,
    page: () => const DeviceFingerprintExamplePage(),
    binding: DeviceFingerprintBinding(),
  ),

  /// 网易滑块设备ID示例
  GetPage(
    name: AppRoutes.neteaseSliderExamplePage,
    page: () => const NeteaseSliderExamplePage(),
  ),

  /// KYC示例
  GetPage(
    name: AppRoutes.kycExamplePage,
    page: () => const KycExamplePage(),
  ),

  ///----------------------------扫码------------------------------
  // 扫码
  GetPage(
    name: AppRoutes.scanPage,
    page: () => const ScanPage(),
    binding: ScanBinding(),
    transition: Transition.fadeIn,
  ),

  // 扫码未知结果页面
  GetPage(
    name: AppRoutes.scanResultPage,
    page: () => const ScanResultPage(),
  ),

  ///----------------------------web------------------------------

  ///web
  GetPage(
      name: AppRoutes.webPage,
      page: () => WebViewPage(),
      binding: WebBinding()),
];
