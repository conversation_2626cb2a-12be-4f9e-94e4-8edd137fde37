/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-26 09:13:26
 * @LastEditTime: 2025-08-04 11:19:02
 */

import 'package:flutter/services.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/database/storage/storage_provider.dart';
import 'package:vcb/http/apiService/api_service.dart';
import 'package:vcb/http/apiService/config_service.dart';
import 'package:vcb/modules/auth/auth_controller.dart';
import 'package:vcb/modules/locale/locale_controller.dart';
import 'package:vcb/modules/locale/models/language_model.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/utils/log_utils.dart';

class AppController extends BaseController<ApiService> {
  @override
  void onInit() {
    super.onInit();

    try {
      api = Get.find<ApiService>();
    } catch (e) {
      Log.e(
          'AppController.onInit: ApiService not found, please ensure it is injected.');
    }

    // 初始化认证控制器
    _initAuthController();
  }

  /// 初始化认证控制器
  void _initAuthController() {
    try {
      // 确保 AuthController 已注册
      if (!Get.isRegistered<AuthController>()) {
        Get.put(AuthController(), permanent: true);
      }
    } catch (e) {
      Log.e('AppController._initAuthController: $e');
    }
  }

  /// 获取认证控制器
  AuthController get authController => AuthController.to;

  /// 检查是否已登录
  bool get isLoggedIn => authController.isAuthenticated;

  /// 快速登出方法
  Future<void> logout() async {
    await authController.logout();
  }

  @override
  void loadData() {}

  /// 语言
  static Rx<LanguageModel> languageModel =
      LocaleController.defaultLanguageModel().obs;

  /// 初始化BaseUrl
  void initCoinfigApi() {
    httpRequest<dynamic>(
        showToast: false,
        Get.find<ConfigService>().getConfigBaseUrl(), (value) {
      if (value != null) {
        String baseUrl = value.domain!.api;
        StorageManager.saveValue(key: GetKey.apiBaseUrl, value: baseUrl);
      }
    });

    ///TODO这里获取公钥
  }

  /// 设置屏幕亮度 默认为 0.5
  static Future<void> setBrightness({
    double brightness = CommonConstant.brightness,
  }) async {
    try {
      double current = await ScreenBrightness.instance.application;
      if (current < brightness) {
        await ScreenBrightness.instance
            .setApplicationScreenBrightness(brightness);
      }
    } catch (_) {}
  }

  /// 重置屏幕亮度
  static Future<void> resetBrightness() async {
    await ScreenBrightness.instance.resetApplicationScreenBrightness();
  }

  ///加载测试数据
  static Future<String> loadTestWalletData() async {
    try {
      String testData = await rootBundle.loadString('assets/test/xxx.json');
      return testData;
    } catch (e) {
      return '';
    }
  }
}
