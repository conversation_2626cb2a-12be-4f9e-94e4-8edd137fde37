import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/database/storage/storage_provider.dart';
import 'package:vcb/http/api_config.dart';
import 'package:vcb/res/resource.dart';

enum AppConfigType {
  debug, //临时调试环境
  mock, // mock环境
  dev, // 测试环境
  product, // 生产环境
  googleplay, // Google Play
  testFlight, // iOS TestFlight
  test // 集成测试
}

class _FlavorConfig {
  final String appName;
  final String apiConfigUrl;
  final String apiBaseUrl;

  const _FlavorConfig({
    required this.appName,
    required this.apiConfigUrl,
    required this.apiBaseUrl,
  });
}

class AppConfig {
  AppConfig._internal();

  factory AppConfig() => instance;
  static final AppConfig instance = AppConfig._internal();
  static AppConfigType? _appConfigType;

  // 日志与功能开关
  bool get enableResponseJsonLog => true;
  bool get enableRequestJsonLog => false;
  bool get enableRequestLog => false;
  bool get enableBuglyHttpLog => true;
  bool get enableGetXLog => false;
  bool get enableDapp => true;

  static const Map<AppConfigType, _FlavorConfig> _flavorConfigs = {
    AppConfigType.debug: _FlavorConfig(
      appName: "VCB 调试环境",
      apiConfigUrl: ApiConfig.degbugApiConfigUrl,
      apiBaseUrl: ApiConfig.debugBaseUrl,
    ),
    AppConfigType.mock: _FlavorConfig(
      appName: "VCB mock环境",
      apiConfigUrl: ApiConfig.mockApiConfigUrl,
      apiBaseUrl: ApiConfig.mockBaseUrl,
    ),
    AppConfigType.dev: _FlavorConfig(
      appName: "VCB 测试环境",
      apiConfigUrl: ApiConfig.devApiConfigUrl,
      apiBaseUrl: ApiConfig.devBaseUrl,
    ),
    AppConfigType.product: _FlavorConfig(
      appName: "VCB",
      apiConfigUrl: ApiConfig.productApiConfigUrl,
      apiBaseUrl: ApiConfig.productBaseUrl,
    ),
    AppConfigType.googleplay: _FlavorConfig(
      appName: "VCB",
      apiConfigUrl: ApiConfig.productApiConfigUrl,
      apiBaseUrl: ApiConfig.productBaseUrl,
    ),
    AppConfigType.testFlight: _FlavorConfig(
      appName: "VCB",
      apiConfigUrl: ApiConfig.productApiConfigUrl,
      apiBaseUrl: ApiConfig.productBaseUrl,
    ),
    AppConfigType.test: _FlavorConfig(
      appName: "VCB",
      apiConfigUrl: ApiConfig.productApiConfigUrl,
      apiBaseUrl: ApiConfig.productBaseUrl,
    ),
  };

  static set appConfigType(AppConfigType value) {
    if (_appConfigType != null) {
      throw Exception("AppConfig has already been initialized.");
    }
    _appConfigType = value;
  }

  /// 设置 ApiConfig 的 baseUrl
  void setApiConfig() {
    ApiConfig.configBaseUrl = apiConfigBaseUrl;
    ApiConfig.baseUrl = apiBaseUrl;
  }

  /// 获取当前环境配置
  _FlavorConfig get _config {
    assert(_appConfigType != null, "AppConfigType must be set.");
    return _flavorConfigs[_appConfigType]!;
  }

  /// 获取 apiConfigBaseUrl
  String get apiConfigBaseUrl => _config.apiConfigUrl;

  /// 获取 apiBaseUrl，优先取本地存储
  String get apiBaseUrl {
    final local = StorageManager.getValue(key: GetKey.apiBaseUrl);
    return local ?? _config.apiBaseUrl;
  }

  // 环境判断
  bool get isDegbugMode => _appConfigType == AppConfigType.debug; //和后端调试模式
  bool get isMock => _appConfigType == AppConfigType.mock;
  bool get isDev => _appConfigType == AppConfigType.dev;
  bool get isProduct => _appConfigType == AppConfigType.product;
  bool get isIosTestFlight => _appConfigType == AppConfigType.testFlight;
  bool get isGoogleplay => _appConfigType == AppConfigType.googleplay;
  bool get isTestMode => _appConfigType == AppConfigType.test;
  bool get isShowExample => true;

  /// 获取环境标识
  String get getFlavor {
    switch (_appConfigType) {
      case AppConfigType.testFlight:
        return Flavor.testFlight;
      case AppConfigType.googleplay:
        return Flavor.googlePlay;
      default:
        return GetPlatform.isAndroid ? Flavor.official : Flavor.appStore;
    }
  }

  /// 获取环境后缀
  String get getFlavorName {
    switch (_appConfigType) {
      case AppConfigType.testFlight:
        return "(TF)";
      case AppConfigType.debug:
        return "(Debug)";
      case AppConfigType.mock:
        return "(Mock)";
      case AppConfigType.dev:
        return GetPlatform.isAndroid ? "(Dev)" : "(TF_Dev)";
      case AppConfigType.test:
        return "(TestMode)";
      case AppConfigType.googleplay:
        return "(GooglePlay)";
      default:
        return "";
    }
  }
}
