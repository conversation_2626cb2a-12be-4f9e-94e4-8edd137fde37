/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 14:48:38
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vcb/app/app_config.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateful_widget.dart';
import 'package:vcb/base/state/keep_alive_wrapper.dart';
import 'package:vcb/main/main_controller.dart';
import 'package:vcb/modules/assets/assets_page.dart';
import 'package:vcb/modules/earn/earn_page.dart';
import 'package:vcb/modules/example/example_page.dart';
import 'package:vcb/modules/loan/loan_page.dart';
import 'package:vcb/modules/profile/profile_page.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/image/image_widget.dart';

class MainPage extends BaseStatefulWidget<MainController> {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        body: Obx(() => PopScope(
              canPop: false,
              onPopInvokedWithResult: (didPop, result) async {
                if (result != null) {
                  return; // 如果 result 不为空，直接返回
                }
                if (didPop) {
                  return;
                }
                if (controller.lastPopTime == null ||
                    DateTime.now().difference(controller.lastPopTime!) >
                        const Duration(seconds: 2)) {
                  // 存储当前按下back键的时间
                  controller.lastPopTime = DateTime.now();
                  // toast
                  Get.showToast(ID.exitApplication.tr);
                } else {
                  controller.lastPopTime = DateTime.now();
                  // 退出app
                  await SystemNavigator.pop();
                }
              },
              child: Scaffold(
                body: KeepAliveWrapper(
                  child: PageView(
                      controller: controller.pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        if (AppConfig.instance.isShowExample)
                          const ExamplePage(),
                        const AssetsPage(),
                        const EarnPage(),
                        const LoanPage(),
                        const ProfilePage(),
                      ]),
                ),
                bottomNavigationBar: Theme(
                  data: ThemeData(
                    splashColor: Colors.transparent,
                  ),
                  child: BottomNavigationBar(
                    backgroundColor: Get.theme.bgColor,
                    type: BottomNavigationBarType.fixed,
                    currentIndex: controller.curPage.value,
                    items: [
                      if (AppConfig.instance.isShowExample)
                        BottomNavigationBarItem(
                          icon: ImageWidget(
                            assetUrl: 'tab_profile_normal',
                            width: Get.setImageSize(24),
                            height: Get.setImageSize(24),
                          ),
                          activeIcon: ImageWidget(
                            assetUrl: 'tab_profile',
                            width: Get.setImageSize(24),
                            height: Get.setImageSize(24),
                          ),
                          label: "example",
                        ),
                      BottomNavigationBarItem(
                          icon: ImageWidget(
                            assetUrl: 'tab_wallet_normal',
                            width: Get.setImageSize(24),
                            height: Get.setImageSize(24),
                          ),
                          activeIcon: ImageWidget(
                            assetUrl: 'tab_wallet',
                            width: Get.setImageSize(24),
                            height: Get.setImageSize(24),
                          ),
                          label: ID.stringAssets.tr),
                      BottomNavigationBarItem(
                          icon: ImageWidget(
                            assetUrl: 'tab_earn_normal',
                            width: Get.setImageSize(28),
                            height: Get.setImageSize(28),
                          ),
                          activeIcon: ImageWidget(
                            assetUrl: 'tab_earn',
                            width: Get.setImageSize(24),
                            height: Get.setImageSize(24),
                          ),
                          label: ID.stringEarn.tr),
                      BottomNavigationBarItem(
                        icon: ImageWidget(
                          assetUrl: 'tab_loan_normal',
                          width: Get.setImageSize(24),
                          height: Get.setImageSize(24),
                        ),
                        activeIcon: ImageWidget(
                          assetUrl: 'tab_loan',
                          width: Get.setImageSize(24),
                          height: Get.setImageSize(24),
                        ),
                        label: ID.stringLoan.tr,
                      ),
                      BottomNavigationBarItem(
                        icon: ImageWidget(
                          assetUrl: 'tab_profile_normal',
                          width: Get.setImageSize(24),
                          height: Get.setImageSize(24),
                        ),
                        activeIcon: ImageWidget(
                          assetUrl: 'tab_profile',
                          width: Get.setImageSize(24),
                          height: Get.setImageSize(24),
                        ),
                        label: ID.stringProfile.tr,
                      ),
                    ],
                    selectedItemColor: Get.theme.textPrimary, // 设置选中项的颜色
                    unselectedItemColor: Get.theme.textTertiary, // 设置未选中项的颜色
                    selectedLabelStyle: TextStyle(
                        fontWeight: FontWeightX.medium,
                        fontSize: Get.setFontSize(11)),
                    unselectedLabelStyle: TextStyle(
                        fontWeight: FontWeightX.medium,
                        fontSize: Get.setFontSize(11)),
                    onTap: (int index) =>
                        controller.navigateToPage(context, index: index),
                  ),
                ),
              ),
            )));
  }
}
