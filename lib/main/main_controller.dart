/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 17:49:47
 */
import 'package:flutter/cupertino.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/database/storage/storage_provider.dart';
import 'package:vcb/modules/assets/assets_controller.dart';
import 'package:vcb/modules/earn/earn_controller.dart';
import 'package:vcb/modules/example/example_controller.dart';
import 'package:vcb/modules/loan/loan_controller.dart';
import 'package:vcb/modules/profile/profile_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/utils/keyboard_util.dart';

class MainController extends BaseController {
  DateTime? lastPopTime;
  final RxInt curPage = 0.obs;
  final RxInt oldPage = 0.obs;
  final PageController pageController = PageController(initialPage: 0);

  @override
  void onInit() async {
    super.onInit();
    _handleFirstLaunch();
    Get.appController.initCoinfigApi();
  }

  void _handleFirstLaunch() {
    if (Get.isFirstLaunch()) {
      StorageManager.saveValue(key: StorageKey.fristLaunch, value: false);
    }
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() {}

  void navigateToPage(BuildContext context, {int index = 0}) {
    // 隐藏键盘
    KeyboardUtils.hideKeyboard(context);
    // 跳转到指定页面
    pageController.jumpToPage(index);
    // 更新当前页索引
    curPage.value = index;
  }
}

class MainBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => MainController());
    Get.lazyPut(() => AssetsController());
    Get.lazyPut(() => EarnController());
    Get.lazyPut(() => LoanController());
    Get.lazyPut(() => ProfileController());
    Get.lazyPut(() => ExampleController());
  }
}
