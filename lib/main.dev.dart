/*
 * @description: Do not edit
 * @Author: wangdog<PERSON>henng
 * @Date: 2024-01-15 12:59:21
 * @LastEditTime: 2024-08-14 17:27:42
 */

import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vcb/app/app_config.dart';
import 'package:vcb/main.dart' show initAppService, MyApp;

/// 切换为测试环境入口
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  AppConfig.appConfigType = AppConfigType.dev;
  AppConfig.instance.setApiConfig();
  // 复用主入口的启动流程
  await initAppService();
  runApp(const MyApp());
}
