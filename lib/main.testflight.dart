/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-08-21 09:23:49
 */

import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vcb/app/app_config.dart';
import 'package:vcb/main.dart';

/// ios TestFlight入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  AppConfig.appConfigType = AppConfigType.testFlight;
  AppConfig.instance.setApiConfig();
  // 复用主入口的启动流程
  await initAppService();
  runApp(const MyApp());
}
