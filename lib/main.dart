/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-08-04 13:06:43
 */
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:oktoast/oktoast.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:vcb/app/app_config.dart';
import 'package:vcb/app/app_service.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/modules/locale/locale_controller.dart';
import 'package:vcb/modules/splash/splash_screen_controller.dart';
import 'package:vcb/modules/splash/splash_screen_page.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/route/pages.dart';
import 'package:vcb/widgets/pull_refresh/refresh_config.dart';

/// 应用入口
Future<void> main() async {
  // 捕获全局异常并上报 Bugly
  FlutterBugly.postCatchedException(() async {
    WidgetsFlutterBinding.ensureInitialized();
    await GetStorage.init();
    AppConfig.appConfigType = AppConfigType.product; // 默认生产环境
    AppConfig.instance.setApiConfig();
    await initAppService();
    // 初始化 Bugly
    await FlutterBugly.init(
      androidAppId: Get.getEnvByKey(SDKConstant.BuglyAndroidAppId),
      iOSAppId: Get.getEnvByKey(SDKConstant.BuglyIOSAppId),
    );
    runApp(const MyApp());
  });
}

/// 初始化全局服务（供多入口调用）
Future<void> initAppService() async {
  await Get.putAsync<AppService>(() async => await AppService().init());
}

/// 根Widget
class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      enableLog: AppConfig.instance.enableGetXLog && kDebugMode,
      debugShowCheckedModeBanner: AppConfig.instance.isDev,
      getPages: routerPages,
      defaultTransition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
      title: 'VCB',
      builder: (context, child) => _buildAppWrapper(context, child),
      darkTheme: darkTheme,
      theme: lightTheme,
      themeMode: ThemeMode.light,
      translations: TranslationsMessage(),
      locale: LocaleController.locale(),
      fallbackLocale: const Locale('en', 'US'),
      localizationsDelegates: const [
        RefreshLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocaleController.supportedLocales,
      initialBinding: SplashScreenBinding(),
      home: const SplashScreenPage(),
    );
  }

  /// 多层包装，统一全局配置
  Widget _buildAppWrapper(BuildContext context, Widget? child) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(
        textScaler: const TextScaler.linear(1.0), // 禁止跟随系统字体大小
      ),
      child: ShowCaseWidget(
        builder: (context) => OKToast(
          dismissOtherOnShow: true,
          child: EasyLoading.init()(
            context,
            RefreshConfig(
              child: ScreenUtilInit(
                fontSizeResolver: (fontSize, instance) =>
                    FontSizeResolvers.radius(fontSize, instance),
                designSize: const Size(375, 812),
                builder: (_, __) => child!,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
