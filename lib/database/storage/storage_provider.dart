/*
 * @description: 本地存储管理器 - 基于GetStorage的非敏感数据存储
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'package:get_storage/get_storage.dart';
import 'package:vcb/database/storage/models/storage_base_model.dart';

/// 本地存储管理器
/// - 适用于非敏感数据的存储（配置、缓存、历史记录等）
/// - 支持基础数据类型和自定义对象的存储
///
/// 存储策略：
/// - 非敏感数据：应用配置、用户偏好、历史记录 → StorageManager
/// - 敏感数据：用户令牌、密码、设备ID → 需要存储在 SecureStorageService
///
/// 支持的数据类型：
/// - 基础类型：String、int、double、bool
/// - 集合类型：Map、List
/// - 自定义对象：继承BaseStorageModel的类
/// ```
class StorageManager {
  /// GetStorage实例
  static final _box = GetStorage();

  // ==================== 基础数据存储 ====================

  /// 保存基础数据类型到本地存储
  ///
  /// [key] 存储键名
  /// [value] 要保存的值，支持String、int、double、bool、Map、List等类型
  ///
  /// Returns: 保存的值
  ///
  /// 说明：适用于应用配置、用户偏好等非敏感数据
  static Future<T> saveValue<T>({required String key, required T value}) async {
    await _box.write(key, value);
    return value;
  }

  /// 获取基础数据类型
  ///
  /// [key] 存储键名
  ///
  /// Returns: 存储的值，如果不存在则返回null
  ///
  /// 说明：支持泛型，自动转换为指定类型
  static T? getValue<T>({required String key}) => _box.read(key);

  // ==================== 对象存储 ====================

  /// 保存自定义对象到本地存储
  ///
  /// [key] 存储键名
  /// [obj] 要保存的对象，必须继承BaseStorageModel
  ///
  /// Returns: 保存的对象
  ///
  /// 说明：对象会自动序列化为JSON格式存储
  static Future saveObject<T extends BaseStorageModel>(
      {required String key, required T obj}) async {
    await _box.write(key, obj.toJson());
    return obj;
  }

  /// 获取自定义对象
  ///
  /// [key] 存储键名
  /// [fromJson] JSON反序列化函数
  ///
  /// Returns: 反序列化后的对象，如果不存在或格式错误则返回null
  ///
  /// 说明：自动将JSON数据反序列化为指定对象类型
  static T? getObject<T extends BaseStorageModel>({
    required String key,
    required T Function(Map<String, dynamic>) fromJson,
  }) {
    final dynamic jsonDynamic = _box.read(key);
    if (jsonDynamic == null) return null;
    if (jsonDynamic is! Map<String, dynamic>) {
      throw Exception(
          'Data retrieved from storage is not a Map<String, dynamic>');
    }
    return fromJson(jsonDynamic);
  }

  // ==================== 数据管理 ====================

  /// 删除指定键的数据
  ///
  /// [key] 要删除的存储键名
  ///
  /// 说明：彻底删除指定键的所有数据
  static Future remove({required String key}) async => _box.remove(key);

  // ==================== 字符串列表管理 ====================

  /// 添加字符串到列表（去重）
  ///
  /// [key] 存储键名
  /// [value] 要添加的字符串值
  ///
  /// 说明：如果值已存在则不会重复添加，适用于收藏、历史记录等场景
  static Future<void> saveStringList(
      {required String key, required String value}) async {
    List<String> currentList = getValue<List<String>>(key: key) ?? [];
    if (!currentList.contains(value)) {
      // 确保不重复
      currentList.add(value);
      await saveValue(key: key, value: currentList);
    }
  }

  /// 添加字符串到列表（限制最大数量）
  ///
  /// [key] 存储键名
  /// [value] 要添加的字符串值
  ///
  /// 说明：
  /// - 最大存储10条记录
  /// - 超出限制时自动删除最旧的记录（FIFO）
  /// - 自动去重，不会添加重复值
  /// - 适用于搜索历史、最近访问等场景
  static Future<void> addStringToListLimit(
      {required String key, required String value}) async {
    // 获取当前列表并转换为字符串类型
    List<dynamic>? dynamicList = getValue<List<dynamic>>(key: key);

    // 初始化当前列表，确保每个元素都是字符串类型
    List<String> currentList = dynamicList?.map((item) {
          if (item is String) {
            return item;
          } else {
            // 处理非字符串类型的情况
            throw Exception('Item is not a String: $item');
          }
        }).toList() ??
        [];

    // 如果列表已满（≥10条），移除最旧的条目
    if (currentList.length >= 10) {
      currentList.removeAt(0); // 移除第一条（最旧的）
    }

    // 添加新的条目（确保不重复）
    if (!currentList.contains(value)) {
      currentList.add(value);
      await saveValue(key: key, value: currentList);
    }
  }

  /// 获取字符串列表
  ///
  /// [key] 存储键名
  ///
  /// Returns: 字符串列表，如果不存在则返回空列表
  ///
  /// 说明：自动处理类型转换，确保返回的都是字符串类型
  static List<String> getStringList({required String key}) {
    // 获取动态类型的列表
    List<dynamic>? dynamicList = getValue<List<dynamic>>(key: key);

    // 如果列表为空，返回空列表
    if (dynamicList == null) {
      return [];
    }

    // 将每个元素转换为字符串并创建新列表
    List<String> stringList = dynamicList.map((item) {
      if (item is String) {
        return item;
      } else {
        // 处理非字符串类型的情况
        throw Exception('Item is not a String: $item');
      }
    }).toList();
    return stringList;
  }

  /// 获取字符串列表（倒序）
  ///
  /// [key] 存储键名
  ///
  /// Returns: 倒序的字符串列表，最新的在前面
  ///
  /// 说明：适用于显示最近记录，如搜索历史、访问记录等
  static List<String> getStringReversedList({required String key}) {
    // 返回倒序列表
    return getStringList(key: key).reversed.toList();
  }

  /// 清空字符串列表
  ///
  /// [key] 存储键名
  ///
  /// 说明：完全删除指定键的列表数据
  static Future<void> clearStringList({required String key}) async {
    await _box.remove(key);
  }
}
