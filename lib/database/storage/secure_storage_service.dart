/*
 * @author: Chend
 * @description: 安全存储服务
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vcb/utils/log_utils.dart';

/*
 * @description: 安全存储服务 - 专门用于敏感数据的加密存储
 *
 * 存储内容：
 * - 敏感数据：用户认证令牌、设备ID、密码等 → SecureStorageService（本服务）
 * - 普通数据：应用配置、缓存、历史记录等 → StorageManager
 *
 * 安全特性：
 * - 基于FlutterSecureStorage，数据加密存储
 * - iOS存储在Keychain，Android使用EncryptedSharedPreferences
 * - 防止恶意应用读取敏感信息
 */
class SecureStorageService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static final GetStorage _localStorage = GetStorage();

  // 安全存储的键名
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _deviceIdKey = 'device_id';
  static const String _deviceFingerprintKey = 'device_fingerprint';

  // 本地存储的键名
  static const String _userInfoKey = 'user_info';
  static const String _deviceNameKey = 'device_name';

  /// 保存访问令牌
  static Future<void> saveAccessToken(String token) async {
    try {
      await _secureStorage.write(key: _accessTokenKey, value: token);
      Log.logPrint("访问令牌已保存");
    } catch (e) {
      Log.e("保存访问令牌失败: $e");
    }
  }

  /// 获取访问令牌
  static Future<String?> getAccessToken() async {
    try {
      return await _secureStorage.read(key: _accessTokenKey);
    } catch (e) {
      Log.e("获取访问令牌失败: $e");
      return null;
    }
  }

  /// 保存刷新令牌
  static Future<void> saveRefreshToken(String token) async {
    try {
      await _secureStorage.write(key: _refreshTokenKey, value: token);
      Log.logPrint("刷新令牌已保存");
    } catch (e) {
      Log.e("保存刷新令牌失败: $e");
    }
  }

  /// 获取刷新令牌
  static Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: _refreshTokenKey);
    } catch (e) {
      Log.e("获取刷新令牌失败: $e");
      return null;
    }
  }

  /// 保存设备ID
  static Future<void> saveDeviceId(String deviceId) async {
    try {
      await _secureStorage.write(key: _deviceIdKey, value: deviceId);
      Log.logPrint("设备ID已保存: $deviceId");
    } catch (e) {
      Log.e("保存设备ID失败: $e");
    }
  }

  /// 获取设备ID
  static Future<String?> getDeviceId() async {
    try {
      return await _secureStorage.read(key: _deviceIdKey);
    } catch (e) {
      Log.e("获取设备ID失败: $e");
      return null;
    }
  }

  /// 保存设备指纹
  static Future<void> saveDeviceFingerprint(String fingerprint) async {
    try {
      await _secureStorage.write(
          key: _deviceFingerprintKey, value: fingerprint);
      Log.logPrint("设备指纹已保存");
    } catch (e) {
      Log.e("保存设备指纹失败: $e");
    }
  }

  /// 获取设备指纹
  static Future<String?> getDeviceFingerprint() async {
    try {
      return await _secureStorage.read(key: _deviceFingerprintKey);
    } catch (e) {
      Log.e("获取设备指纹失败: $e");
      return null;
    }
  }

  // ==================== 用户信息管理 ====================

  /// 保存用户信息到本地存储
  ///
  /// [userInfo] 用户信息Map，通常包含id、email、displayName等字段
  ///
  /// 说明：用户信息为非敏感数据，存储在本地存储中便于快速访问
  static void saveUserInfo(Map<String, dynamic> userInfo) {
    try {
      _localStorage.write(_userInfoKey, userInfo);
      Log.logPrint("用户信息已保存: ${userInfo['email']}");
    } catch (e) {
      Log.e("保存用户信息失败: $e");
    }
  }

  /// 获取用户信息
  ///
  /// Returns: 用户信息Map，如果不存在或读取失败则返回null
  ///
  /// 说明：返回完整的用户信息，包含id、email、displayName等字段
  static Map<String, dynamic>? getUserInfo() {
    try {
      final data = _localStorage.read(_userInfoKey);
      if (data != null && data is Map<String, dynamic>) {
        return Map<String, dynamic>.from(data);
      }
      return null;
    } catch (e) {
      Log.e("获取用户信息失败: $e");
      return null;
    }
  }

  /// 保存设备名称到本地存储
  ///
  /// [deviceName] 设备名称字符串
  ///
  /// 说明：设备名称为非敏感信息，用于显示和日志记录
  static void saveDeviceName(String deviceName) {
    try {
      _localStorage.write(_deviceNameKey, deviceName);
      Log.logPrint("设备名称已保存: $deviceName");
    } catch (e) {
      Log.e("保存设备名称失败: $e");
    }
  }

  /// 获取设备名称
  ///
  /// Returns: 设备名称字符串，如果不存在或读取失败则返回null
  ///
  /// 说明：用于UI显示和日志记录
  static String? getDeviceName() {
    try {
      return _localStorage.read(_deviceNameKey);
    } catch (e) {
      Log.e("获取设备名称失败: $e");
      return null;
    }
  }

  // ==================== 工具方法 ====================

  /// 检查用户是否已登录
  ///
  /// Returns: true表示已登录，false表示未登录
  ///
  /// 说明：通过检查访问令牌是否存在且非空来判断登录状态
  static Future<bool> isLoggedIn() async {
    final token = await getAccessToken();
    return token != null && token.isNotEmpty;
  }

  /// 清除所有认证相关信息
  ///
  /// 说明：清除访问令牌、刷新令牌和用户信息，但保留设备信息
  /// 适用于用户登出场景
  static Future<void> clearAuthData() async {
    try {
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      _localStorage.remove(_userInfoKey);
      Log.logPrint("认证信息已清除");
    } catch (e) {
      Log.e("清除认证信息失败: $e");
    }
  }

  /// 清除所有存储数据
  ///
  /// 说明：清除所有安全存储和本地存储的数据，包括设备信息
  /// 适用于应用重置或卸载场景
  ///
  /// ⚠️ 警告：此操作不可逆，会删除所有本地数据
  static Future<void> clearAllData() async {
    try {
      await _secureStorage.deleteAll();
      await _localStorage.erase();
      Log.logPrint("所有数据已清除");
    } catch (e) {
      Log.e("清除所有数据失败: $e");
    }
  }
}
