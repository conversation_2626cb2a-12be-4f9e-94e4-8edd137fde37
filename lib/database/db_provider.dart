// ignore_for_file: depend_on_referenced_packages

/*
 * @description: Do not edit
 * @Author: wangdog<PERSON>henng
 * @Date: 2024-01-28 22:34:53
 * @LastEditTime: 2025-08-04 13:54:51
 */

import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart' as pp;

part 'db_provider.g.dart';

@DriftDatabase(tables: [], daos: [], include: {'./drift/sql.drift'})
class AppDatabase extends _$AppDatabase {
  // 私有构造函数
  AppDatabase._internal(super.e);

  // 私有静态变量
  static AppDatabase? _instance;

  // 公共的工厂构造函数
  factory AppDatabase(QueryExecutor e) {
    return _instance ??= AppDatabase._internal(e);
  }

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
          onUpgrade: (Migrator m, int oldVersion, int newVersion) async {
        ///
      });
}

LazyDatabase openConnection() {
  // 我们创建一个LazyDatabase，它在需要时才会打开数据库。
  return LazyDatabase(() async {
    // 获取应用的文档目录，并使用它来构建数据库文件的路径。
    final dbFolder = await pp.getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'vcb.sqlite'));
    return NativeDatabase(file);
  });
}
