/*
 * @author: Chend
 * @description: Example 模块控制器
 * @LastEditTime: 2025-01-XX XX:XX:XX
 */
import 'package:get/get.dart';
import 'package:vcb/base/controllers/base_controller.dart';

class ExampleController extends BaseController {
  // 状态变量
  final isLoading = false.obs;
  final currentExample = ''.obs;
  final exampleList = <String>[].obs;
  final errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeExamples();
  }

  /// 初始化示例列表
  void _initializeExamples() {
    exampleList.value = [
      '通行密钥',
      'UI 组件示例',
      '设备指纹示例',
      '网易滑块设备ID示例',
      'KYC示例',
    ];
  }

  @override
  void loadData() {
    // 可以在这里加载其他数据
  }
}

class ExampleBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ExampleController());
  }
}
