/*
 * @author: <PERSON><PERSON>
 * @description: UI 组件示例页面
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class UIComponentsExamplePage extends StatelessWidget {
  const UIComponentsExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: 'UI 组件示例',
        hideLeading: false,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'UI 组件示例',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 30),

            // 按钮示例
            Text(
              '按钮组件',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16),
            ButtonWidget(
              width: 200,
              text: '主要按钮',
              onPressed: () {
                Get.showSnackbar(
                  GetSnackBar(
                    title: 'UI 组件',
                    message: '主要按钮点击',
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
            SizedBox(height: 16),
            ButtonWidget(
              width: 200,
              text: '次要按钮',
              onPressed: () {
                Get.showSnackbar(
                  GetSnackBar(
                    title: 'UI 组件',
                    message: '次要按钮点击',
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
            SizedBox(height: 30),

            // 文本示例
            Text(
              '文本组件',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16),
            Text('这是普通文本'),
            Text(
              '这是加粗文本',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              '这是彩色文本',
              style: TextStyle(color: Colors.blue),
            ),
            SizedBox(height: 30),

            // 输入框示例
            Text(
              '输入框组件',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: '用户名',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              obscureText: true,
              decoration: InputDecoration(
                labelText: '密码',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
