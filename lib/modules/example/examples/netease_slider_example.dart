/*
 * @author: Chen<PERSON>
 * @description: 网易滑块设备ID示例页面
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';
import 'package:vcb/widgets/button/button_widget.dart';
import 'package:vcb/utils/captcha_utils.dart';

class NeteaseSliderExamplePage extends StatefulWidget {
  const NeteaseSliderExamplePage({super.key});

  @override
  State<NeteaseSliderExamplePage> createState() =>
      _NeteaseSliderExamplePageState();
}

class _NeteaseSliderExamplePageState extends State<NeteaseSliderExamplePage> {
  String captchaResult = '未验证';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: '网易滑块设备ID示例',
        hideLeading: false,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '网易滑块验证示例',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 40),
            Text("验证结果：$captchaResult"),
            SizedBox(height: 40),
            ButtonWidget(
              width: 200,
              text: '滑动验证',
              onPressed: () {
                CaptchaUtils.showCaptcha();
                setState(() {
                  captchaResult = '验证中...';
                });
                // 模拟验证结果
                Future.delayed(Duration(seconds: 2), () {
                  if (mounted) {
                    setState(() {
                      captchaResult = '验证成功';
                    });
                  }
                });
              },
            ),
            SizedBox(height: 20),
            ButtonWidget(
              width: 200,
              text: '重置验证',
              onPressed: () {
                setState(() {
                  captchaResult = '未验证';
                });
                Get.showSnackbar(
                  GetSnackBar(
                    title: '滑块验证',
                    message: '验证状态已重置',
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
            SizedBox(height: 40),
            Container(
              padding: EdgeInsets.all(16),
              margin: EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '功能说明：',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text('• 点击"滑动验证"按钮启动验证'),
                  Text('• 完成滑块验证获取设备ID'),
                  Text('• 可重置验证状态重新测试'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
