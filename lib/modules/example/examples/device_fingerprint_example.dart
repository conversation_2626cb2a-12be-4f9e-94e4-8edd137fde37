/*
 * @author: Chen<PERSON>
 * @description: 设备指纹示例页面
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/example/controllers/device_fingerprint_controller.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class DeviceFingerprintExamplePage
    extends BaseStatelessWidget<DeviceFingerprintController> {
  const DeviceFingerprintExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: '设备指纹示例',
        hideLeading: false,
      ),
      body: Obx(
        () => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '设备指纹示例',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 40),
              Text("设备ID：${controller.deviceId.value}"),
              SizedBox(height: 20),
              Text("设备指纹：${controller.fingerprint.value}"),
              SizedBox(height: 20),
              Text("设备名：${controller.deviceName.value}"),
              SizedBox(height: 20),
              Text("平台:${GetPlatform.isAndroid ? "Android" : "IOS"}"),
              SizedBox(height: 40),
              ButtonWidget(
                width: 200,
                text: '获取设备ID',
                onPressed: () {
                  controller.getDeviceId();
                },
              ),
              SizedBox(height: 20),
              Obx(() => ButtonWidget(
                    buttonStatus: controller.fpButtonStatus.value,
                    width: 200,
                    text: '获取设备指纹',
                    onPressed: () {
                      controller.fingerprintAction();
                    },
                  )),
              SizedBox(height: 20),
              ButtonWidget(
                width: 200,
                text: '获取设备名',
                onPressed: () {
                  controller.getDeviceName();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
