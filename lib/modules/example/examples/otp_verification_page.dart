/*
 * @author: Chend
 * @description: OTP验证页面 - 邮箱验证码验证
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/modules/example/controllers/passkey_auth_controller.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class OtpVerificationPage extends StatelessWidget {
  final String email;
  final String password;
  final bool isRegister; // true: 注册, false: 登录

  const OtpVerificationPage({
    super.key,
    required this.email,
    required this.password,
    required this.isRegister,
  });

  @override
  Widget build(BuildContext context) {
    // 确保控制器存在，如果不存在则创建
    final controller = Get.isRegistered<PasskeyAuthController>()
        ? Get.find<PasskeyAuthController>()
        : Get.put(PasskeyAuthController());
    final otpController = TextEditingController();

    // 页面初始化时自动发送OTP
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.email.value = email;
      controller.password.value = password;
      controller.sendEmailOtp();
    });

    return Scaffold(
      appBar: AppBar(
        title: Text('邮箱验证'),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 40),

            // 标题和说明
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.email_outlined,
                    size: 80,
                    color: Colors.blue,
                  ),
                  SizedBox(height: 20),
                  Text(
                    '验证您的邮箱',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 10),
                  Text(
                    '我们已向 $email 发送了验证码',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  Text(
                    '请输入6位验证码',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 40),

            // OTP输入框
            Text(
              '验证码：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            TextField(
              controller: otpController,
              decoration: InputDecoration(
                hintText: '请输入6位验证码',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Icon(Icons.security),
                suffixText: '测试码: 123456',
                suffixStyle: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,
                ),
              ),
              keyboardType: TextInputType.number,
              maxLength: 6,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                letterSpacing: 2,
              ),
              onChanged: (value) {
                controller.otpCode.value = value;
              },
            ),

            SizedBox(height: 20),

            // 重新发送按钮
            Center(
              child: Obx(() => TextButton(
                    onPressed: controller.canResendOtp.value
                        ? () => controller.sendEmailOtp()
                        : null,
                    child: Text(
                      controller.canResendOtp.value
                          ? '重新发送验证码'
                          : '重新发送 (${controller.otpCountdown.value}s)',
                      style: TextStyle(
                        color: controller.canResendOtp.value
                            ? Colors.blue
                            : Colors.grey,
                      ),
                    ),
                  )),
            ),

            SizedBox(height: 30),

            // 验证按钮
            SizedBox(
              width: double.infinity,
              child: Obx(() => ButtonWidget(
                    buttonStatus: controller.verifyOtpButtonStatus.value,
                    text: '验证并继续',
                    onPressed: () => _verifyAndContinue(controller),
                  )),
            ),

            SizedBox(height: 20),

            // 状态消息
            Center(
              child: Obx(() => Text(
                    controller.statusMessage.value,
                    style: TextStyle(
                      color: controller.statusMessage.value.contains('成功')
                          ? Colors.green
                          : Colors.red,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  )),
            ),

            Spacer(),

            // 底部提示
            Center(
              child: Column(
                children: [
                  Text(
                    '没有收到验证码？',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 5),
                  Text(
                    '请检查垃圾邮件文件夹或稍后重试',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 验证OTP并继续到Passkey页面
  void _verifyAndContinue(PasskeyAuthController controller) async {
    final success = await controller.verifyEmailOtp();

    if (success) {
      // OTP验证成功，跳转到Passkey认证页面
      Get.to(() => PasskeyAuthPage(
            email: email,
            password: password,
            isRegister: isRegister,
          ));
    }
    // 如果验证失败，错误信息已经在controller中显示
  }
}

/// Passkey认证页面
class PasskeyAuthPage extends StatelessWidget {
  final String email;
  final String password;
  final bool isRegister;

  const PasskeyAuthPage({
    super.key,
    required this.email,
    required this.password,
    required this.isRegister,
  });

  @override
  Widget build(BuildContext context) {
    // 确保控制器存在，如果不存在则创建
    final controller = Get.isRegistered<PasskeyAuthController>()
        ? Get.find<PasskeyAuthController>()
        : Get.put(PasskeyAuthController());

    return Scaffold(
      appBar: AppBar(
        title: Text(isRegister ? 'Passkey 注册' : 'Passkey 登录'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            SizedBox(height: 60),

            // Passkey图标和说明
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.fingerprint,
                    size: 100,
                    color: Colors.blue,
                  ),
                  SizedBox(height: 30),
                  Text(
                    isRegister ? '创建您的 Passkey' : '使用 Passkey 登录',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 15),
                  Text(
                    isRegister
                        ? '使用生物识别创建安全的 Passkey\n用于快速登录'
                        : '使用您的生物识别信息\n快速安全地登录',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            SizedBox(height: 60),

            // Passkey按钮
            SizedBox(
              width: double.infinity,
              child: Obx(() => ButtonWidget(
                    buttonStatus: isRegister
                        ? controller.registerButtonStatus.value
                        : controller.loginButtonStatus.value,
                    text: isRegister ? '创建 Passkey' : '使用 Passkey 登录',
                    onPressed: () => _performPasskeyAuth(controller),
                  )),
            ),

            SizedBox(height: 30),

            // 状态消息
            Center(
              child: Obx(() => Text(
                    controller.statusMessage.value,
                    style: TextStyle(
                      color: controller.statusMessage.value.contains('成功')
                          ? Colors.green
                          : Colors.red,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  )),
            ),

            Spacer(),

            // 底部说明
            Center(
              child: Text(
                'Passkey 使用您设备的生物识别功能\n提供更安全便捷的认证体验',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 执行Passkey认证
  void _performPasskeyAuth(PasskeyAuthController controller) async {
    // 设置邮箱和密码
    controller.email.value = email;
    controller.password.value = password;

    if (isRegister) {
      await controller.register();
    } else {
      await controller.login();
    }

    // 如果成功，controller会自动处理导航
  }
}
