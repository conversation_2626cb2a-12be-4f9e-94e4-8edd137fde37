/*
 * @author: <PERSON><PERSON>
 * @description: 通行密钥示例页面
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/modules/example/controllers/passkey_auth_controller.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class PasskeyExamplePage extends StatelessWidget {
  const PasskeyExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    // 确保控制器正确注册
    final controller = Get.isRegistered<PasskeyAuthController>()
        ? Get.find<PasskeyAuthController>()
        : Get.put(PasskeyAuthController());

    final TextEditingController emailController = TextEditingController();
    final TextEditingController passwordController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: Text('通行密钥示例'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Passkey 认证示例',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 30),

            // 用户状态显示
            Obx(() => Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                    color: controller.isLoggedIn.value
                        ? Colors.green.shade50
                        : Colors.grey.shade50,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '状态：',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        controller.isLoggedIn.value
                            ? '已登录 - ${controller.currentUser.value}'
                            : '未登录',
                        style: TextStyle(
                          fontSize: 14,
                          color: controller.isLoggedIn.value
                              ? Colors.green.shade700
                              : Colors.grey.shade600,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        controller.statusMessage.value,
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                )),

            SizedBox(height: 20),

            // 设备信息显示
            Obx(() => Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.blue.shade300),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.blue.shade50,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '设备信息：',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text('设备ID: ${controller.deviceId.value}'),
                      SizedBox(height: 4),
                      Text('设备名称: ${controller.deviceName.value}'),
                      SizedBox(height: 4),
                      Text(
                          '设备指纹: ${controller.fingerprint.value.length > 20 ? "${controller.fingerprint.value.substring(0, 20)}..." : controller.fingerprint.value}'),
                    ],
                  ),
                )),

            SizedBox(height: 30),

            // 邮箱输入
            Obx(() => !controller.isLoggedIn.value
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '邮箱地址：',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 8),
                      TextField(
                        controller: emailController,
                        decoration: InputDecoration(
                          hintText: '请输入邮箱地址',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.email),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        onChanged: (value) {
                          controller.email.value = value;
                        },
                      ),
                      SizedBox(height: 20),

                      // 密码输入
                      Text(
                        '密码：',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 8),
                      TextField(
                        controller: passwordController,
                        decoration: InputDecoration(
                          hintText: '请输入密码 - 测试：123456',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.lock),
                        ),
                        obscureText: true,
                        onChanged: (value) {
                          controller.password.value = value;
                        },
                      ),

                      SizedBox(height: 30),
                    ],
                  )
                : SizedBox()),

            // 操作按钮
            Center(
              child: Obx(() => !controller.isLoggedIn.value
                  ? Column(
                      children: [
                        // 注册按钮
                        ButtonWidget(
                          buttonStatus: controller.registerButtonStatus.value,
                          width: 200,
                          text: '注册账户',
                          onPressed: () {
                            final email = emailController.text.trim();
                            final password = passwordController.text.trim();
                            controller.startRegister(email, password);
                          },
                        ),
                        SizedBox(height: 20),

                        // 登录按钮
                        ButtonWidget(
                          buttonStatus: controller.loginButtonStatus.value,
                          width: 200,
                          text: '登录账户',
                          onPressed: () {
                            final email = emailController.text.trim();
                            final password = passwordController.text.trim();
                            controller.startLogin(email, password);
                          },
                        ),
                      ],
                    )
                  : Column(
                      children: [
                        // Passkey管理按钮
                        ButtonWidget(
                          buttonStatus: controller.manageButtonStatus.value,
                          width: 200,
                          text: '列出 Passkeys',
                          onPressed: () {
                            controller.managePasskeys('passkey-list');
                          },
                        ),
                        SizedBox(height: 20),

                        ButtonWidget(
                          buttonStatus: controller.manageButtonStatus.value,
                          width: 200,
                          text: '添加 Passkey',
                          onPressed: () {
                            controller.managePasskeys('passkey-append');
                          },
                        ),
                        SizedBox(height: 20),

                        ButtonWidget(
                          buttonStatus: controller.manageButtonStatus.value,
                          width: 200,
                          text: '删除 Passkey',
                          onPressed: () {
                            controller.managePasskeys('passkey-delete');
                          },
                        ),
                        SizedBox(height: 30),

                        // 登出按钮
                        ButtonWidget(
                          width: 200,
                          text: '登出',
                          onPressed: () {
                            controller.logout();
                            emailController.clear();
                          },
                        ),
                      ],
                    )),
            ),

            SizedBox(height: 40),

            // 功能说明
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.blue.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.blue.shade50,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Passkey功能说明：',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text('• 输入邮箱地址进行注册或登录'),
                  Text('• 注册会创建新的Passkey凭据'),
                  Text('• 登录使用已有的Passkey进行认证'),
                  Text('• 登录后可以管理Passkey（列出/添加/删除）'),
                  Text('• 支持生物识别和安全密钥'),
                  SizedBox(height: 8),
                  Text(
                    '注意：这是模拟演示，实际使用需要配置真实的Passkey服务',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange.shade700,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
