/*
 * @author: GPT方案
 * @description: GPT方案的Passkey测试页面
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/modules/example/controllers/gpt_passkey_controller.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class GPTPasskeyPage extends StatelessWidget {
  const GPTPasskeyPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 确保控制器正确注册
    final controller = Get.isRegistered<GPTPasskeyController>()
        ? Get.find<GPTPasskeyController>()
        : Get.put(GPTPasskeyController());

    final TextEditingController emailController = TextEditingController();
    final TextEditingController passwordController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: Text('GPT Passkey 方案测试'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题说明
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.science,
                    size: 60,
                    color: Colors.blue,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'GPT方案测试',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '直接使用passkeys包处理生物识别',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 40),

            // 邮箱输入
            Text(
              '邮箱：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            TextField(
              controller: emailController,
              decoration: InputDecoration(
                hintText: '请输入邮箱地址',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) {
                controller.email.value = value;
              },
            ),

            SizedBox(height: 20),

            // 密码输入（可选）
            Text(
              '密码（可选）：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            TextField(
              controller: passwordController,
              decoration: InputDecoration(
                hintText: '可选密码 - 测试：123456',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
              onChanged: (value) {
                // GPT方案不需要密码字段，忽略
              },
            ),

            SizedBox(height: 40),

            // 按钮区域
            Row(
              children: [
                // 注册按钮
                Expanded(
                  child: Obx(() => ButtonWidget(
                        buttonStatus: controller.registerButtonStatus.value,
                        text: '注册 Passkey',
                        onPressed: () {
                          controller.email.value = emailController.text.trim();
                          controller.quickRegister();
                        },
                      )),
                ),

                SizedBox(width: 16),

                // 登录按钮
                Expanded(
                  child: Obx(() => ButtonWidget(
                        buttonStatus: controller.loginButtonStatus.value,
                        text: '登录 Passkey',
                        onPressed: () {
                          controller.email.value = emailController.text.trim();
                          controller.quickLogin();
                        },
                      )),
                ),
              ],
            ),

            SizedBox(height: 30),

            // 状态显示
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '状态信息：',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                  SizedBox(height: 8),
                  Obx(() => Text(
                        controller.status.value.isEmpty
                            ? '等待操作...'
                            : controller.status.value,
                        style: TextStyle(
                          fontSize: 14,
                          color: controller.status.value.contains('成功')
                              ? Colors.green[700]
                              : controller.status.value.contains('失败') ||
                                      controller.status.value.contains('异常')
                                  ? Colors.red[700]
                                  : Colors.blue[700],
                        ),
                      )),
                ],
              ),
            ),

            SizedBox(height: 20),

            // 加载指示器
            Center(
              child: Obx(() => controller.loading.value
                  ? Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 8),
                        Text(
                          '处理中...',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    )
                  : SizedBox()),
            ),

            Spacer(),

            // 底部说明
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '💡 GPT方案特点：',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '• 直接使用passkeys包处理生物识别\n'
                    '• 绕过Corbado的后端API依赖\n'
                    '• 应该能直接弹出指纹/Face ID界面',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
