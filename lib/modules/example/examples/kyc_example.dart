/*
 * @author: <PERSON><PERSON>
 * @description: KYC示例页面
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.dart';
import 'package:get/get.dart';
import 'package:vcb/utils/log_utils.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class KycExamplePage extends StatefulWidget {
  const KycExamplePage({super.key});

  @override
  State<KycExamplePage> createState() => _KycExamplePageState();
}

class _KycExamplePageState extends State<KycExamplePage> {
  String kycStatus = '未开始';
  String kycResult = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: 'KYC示例',
        hideLeading: false,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'KYC身份验证示例',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 30),

            // 状态显示
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey.shade50,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'KYC状态：',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    kycStatus,
                    style: TextStyle(
                      fontSize: 14,
                      color: _getStatusColor(),
                    ),
                  ),
                  if (kycResult.isNotEmpty) ...[
                    SizedBox(height: 16),
                    Text(
                      '验证结果：',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      kycResult,
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ],
              ),
            ),

            SizedBox(height: 40),

            // 操作按钮
            Center(
              child: Column(
                children: [
                  ButtonWidget(
                    width: 200,
                    text: '开始KYC验证',
                    onPressed: () => _startKycVerification(),
                  ),
                  SizedBox(height: 20),
                  ButtonWidget(
                    width: 200,
                    text: '重置状态',
                    onPressed: () => _resetStatus(),
                  ),
                ],
              ),
            ),

            SizedBox(height: 40),

            // 功能说明
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.blue.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.blue.shade50,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'KYC功能说明：',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text('• 点击"开始KYC验证"启动身份验证流程'),
                  Text('• 支持身份证、护照等证件验证'),
                  Text('• 包含活体检测和人脸识别'),
                  Text('• 验证完成后显示结果状态'),
                  Text('• 可重置状态重新进行验证'),
                  SizedBox(height: 8),
                  Text(
                    '注意：需要配置有效的访问令牌才能正常使用',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange.shade700,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (kycStatus) {
      case '验证成功':
        return Colors.green;
      case '验证失败':
        return Colors.red;
      case '验证中...':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Future<void> _startKycVerification() async {
    setState(() {
      kycStatus = '验证中...';
      kycResult = '';
    });

    try {
      // 这里使用示例访问令牌，实际使用时需要从服务器获取有效令牌
      final String accessToken = '';

      // 令牌过期回调
      onTokenExpiration() async {
        return Future<String>.delayed(
            Duration(seconds: 2), () => "your new access token");
      }

      // 状态变化回调
      onStatusChanged(
          SNSMobileSDKStatus newStatus, SNSMobileSDKStatus prevStatus) {
        Log.logPrint("KYC SDK状态变化: $prevStatus -> $newStatus");
        setState(() {
          kycStatus = '状态: ${newStatus.toString()}';
        });
      }

      // 初始化SDK
      final snsMobileSDK = SNSMobileSDK.init(accessToken, onTokenExpiration)
          .withHandlers(onStatusChanged: onStatusChanged)
          .withDebug(true) // 开启调试模式
          .withLocale(Locale("cn")) // 设置中文
          .build();

      // 启动KYC验证
      final SNSMobileSDKResult result = await snsMobileSDK.launch();

      // 处理验证结果
      setState(() {
        if (result.success) {
          kycStatus = '验证成功';
          kycResult = '验证通过，用户身份确认';
        } else {
          kycStatus = '验证失败';
          kycResult = result.errorMsg ?? '验证过程中出现错误';
        }
      });

      Log.logPrint("KYC验证完成: $result");

      // 显示结果提示
      Get.showSnackbar(
        GetSnackBar(
          title: 'KYC验证',
          message: result.success ? '验证成功' : '验证失败: ${result.errorMsg}',
          duration: Duration(seconds: 3),
          backgroundColor: result.success ? Colors.green : Colors.red,
        ),
      );
    } catch (e) {
      setState(() {
        kycStatus = '验证失败';
        kycResult = '发生异常: $e';
      });

      Log.logPrint("KYC验证异常: $e");

      Get.showSnackbar(
        GetSnackBar(
          title: 'KYC验证',
          message: '验证过程中发生异常',
          duration: Duration(seconds: 3),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _resetStatus() {
    setState(() {
      kycStatus = '未开始';
      kycResult = '';
    });

    Get.showSnackbar(
      GetSnackBar(
        title: 'KYC验证',
        message: '状态已重置',
        duration: Duration(seconds: 2),
      ),
    );
  }
}
