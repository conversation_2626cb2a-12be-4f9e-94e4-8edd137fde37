/*
 * @author: Chen<PERSON>
 * @description: HTTP API服务
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:vcb/database/storage/secure_storage_service.dart';
import 'package:vcb/utils/log_utils.dart';
import 'package:vcb/utils/rsa_util.dart';

class HttpApiService {
  static const String baseUrl =
      'https://pro-6505734815132129676.frontendapi.cloud.corbado.io'; // 替换为实际的API地址
  static final http.Client _client = http.Client();
  static String? _cachedPublicKeyPem;

  /// 通用POST请求
  static Future<Map<String, dynamic>> _post(
    String path,
    Map<String, dynamic> body, {
    Map<String, String>? headers,
    bool needAuth = false,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$path');
      final requestHeaders = {
        'Content-Type': 'application/json',
        if (headers != null) ...headers,
      };

      // 如果需要认证，添加Authorization头
      if (needAuth) {
        final token = await SecureStorageService.getAccessToken();
        if (token != null) {
          requestHeaders['Authorization'] = 'Bearer $token';
        }
      }

      Log.logPrint("POST请求: $path");
      Log.logPrint("请求头: $requestHeaders");
      Log.logPrint("请求体: $body");

      final response = await _client.post(
        uri,
        headers: requestHeaders,
        body: jsonEncode(body),
      );

      Log.logPrint("响应状态码: ${response.statusCode}");
      Log.logPrint("响应体: ${response.body}");

      if (response.statusCode < 200 || response.statusCode >= 300) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      return jsonDecode(response.body) as Map<String, dynamic>;
    } catch (e) {
      Log.e("POST请求失败: $e");
      rethrow;
    }
  }

  /// 获取服务器公钥
  static Future<String> fetchServerPublicKey({
    bool forceRefresh = false,
  }) async {
    if (!forceRefresh && _cachedPublicKeyPem != null) {
      return _cachedPublicKeyPem!;
    }

    try {
      final uri = Uri.parse('$baseUrl/api/public-key');
      final response = await _client.get(
        uri,
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode != 200) {
        throw Exception('获取公钥失败: ${response.statusCode}');
      }

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      _cachedPublicKeyPem = data['publicKey'] as String;

      Log.logPrint("服务器公钥获取成功");
      return _cachedPublicKeyPem!;
    } catch (e) {
      Log.e("获取服务器公钥失败: $e");
      rethrow;
    }
  }

  /// 检查令牌有效性
  static Future<Map<String, dynamic>> checkToken() async {
    try {
      final token = await SecureStorageService.getAccessToken();
      if (token == null || token.isEmpty) {
        return {'success': false, 'message': '无访问令牌'};
      }

      final deviceId = await SecureStorageService.getDeviceId();
      final fingerprint = await SecureStorageService.getDeviceFingerprint();

      final response = await _client.get(
        Uri.parse('$baseUrl/api/auth/check'),
        headers: {
          'Authorization': 'Bearer $token',
          'X-Device-Id': deviceId ?? '',
          'X-Device-Fingerprint': fingerprint ?? '',
        },
      );

      if (response.statusCode == 200) {
        return {'success': true};
      } else {
        return {'success': false, 'message': '令牌无效'};
      }
    } catch (e) {
      Log.e("检查令牌失败，使用模拟响应: $e");
      // 模拟令牌检查：如果有令牌就认为有效
      final token = await SecureStorageService.getAccessToken();
      if (token != null && token.isNotEmpty) {
        return {'success': true, 'message': '令牌有效（模拟）'};
      } else {
        return {'success': false, 'message': '无访问令牌'};
      }
    }
  }

  /// Passkey注册初始化
  static Future<Map<String, dynamic>> registerInit(
    String email,
    String deviceFingerprint,
  ) async {
    try {
      return await _post(
        '/v2/auth/process/init',
        {'email': email},
        headers: {'X-Device-Fingerprint': deviceFingerprint},
      );
    } catch (e) {
      Log.e("注册初始化失败，使用模拟响应: $e");
      // 返回模拟的webauthnOptions
      await Future.delayed(Duration(seconds: 1));
      return {
        'success': true,
        'webauthnOptions': {
          'challenge':
              'mock_challenge_${DateTime.now().millisecondsSinceEpoch}',
          'rp': {'id': 'your-domain.com', 'name': 'VCB App'},
          'user': {
            'id': 'user_${email.hashCode}',
            'name': email,
            'displayName': email.split('@')[0],
          },
          'pubKeyCredParams': [
            {'type': 'public-key', 'alg': -7}, // ES256
            {'type': 'public-key', 'alg': -257}, // RS256
          ],
          'timeout': 60000,
          'attestation': 'direct',
          'authenticatorSelection': {
            'authenticatorAttachment': 'platform',
            'userVerification': 'required',
          },
        },
      };
    }
  }

  /// Passkey注册完成
  static Future<Map<String, dynamic>> registerFinish(
    String email,
    Map<String, dynamic> signedPasskeyData,
    String deviceFingerprint, {
    String? password,
  }) async {
    try {
      final body = {'email': email, 'signedPasskeyData': signedPasskeyData};

      // 如果有密码，进行RSA加密
      if (password != null && password.isNotEmpty) {
        try {
          if (!RSAUtil.isInitialized) {
            final publicKeyPem = await fetchServerPublicKey();
            RSAUtil.init(publicKeyPem);
          }
          body['encryptedPassword'] = RSAUtil.encrypt(password);
        } catch (e) {
          Log.e("RSA加密失败，使用明文密码: $e");
          body['password'] = password; // 如果加密失败，使用明文（仅测试）
        }
      }

      return await _post(
        '/api/passkey/register/finish',
        body,
        headers: {'X-Device-Fingerprint': deviceFingerprint},
      );
    } catch (e) {
      Log.e("注册完成失败，使用模拟响应: $e");
      // 返回模拟成功响应
      await Future.delayed(Duration(seconds: 1));
      return {
        'success': true,
        'accessToken':
            'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
        'refreshToken':
            'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        'user': {
          'id': 'user_${email.hashCode}',
          'email': email,
          'displayName': email.split('@')[0],
        },
      };
    }
  }

  /// Passkey登录初始化
  static Future<Map<String, dynamic>> loginInit(
    String email,
    String deviceFingerprint, {
    String? captchaToken,
  }) async {
    try {
      final body = {'email': email};
      if (captchaToken != null) {
        body['captchaToken'] = captchaToken;
      }

      return await _post(
        '/api/passkey/login/init',
        body,
        headers: {'X-Device-Fingerprint': deviceFingerprint},
      );
    } catch (e) {
      Log.e("登录初始化失败，使用模拟响应: $e");
      // 返回模拟的登录选项
      await Future.delayed(Duration(seconds: 1));
      return {
        'success': true,
        'webauthnOptions': {
          'challenge':
              'mock_login_challenge_${DateTime.now().millisecondsSinceEpoch}',
          'timeout': 60000,
          'rpId': 'your-domain.com',
          'allowCredentials': [], // 后端会填充用户的凭据列表
          'userVerification': 'required',
        },
      };
    }
  }

  /// Passkey登录完成
  static Future<Map<String, dynamic>> loginFinish(
    String email,
    Map<String, dynamic> signedPasskeyData,
    String deviceFingerprint, {
    String? password,
  }) async {
    try {
      final body = {'email': email, 'signedPasskeyData': signedPasskeyData};

      // 如果有密码，进行RSA加密
      if (password != null && password.isNotEmpty) {
        try {
          if (!RSAUtil.isInitialized) {
            final publicKeyPem = await fetchServerPublicKey();
            RSAUtil.init(publicKeyPem);
          }
          body['encryptedPassword'] = RSAUtil.encrypt(password);
        } catch (e) {
          Log.e("RSA加密失败，使用明文密码: $e");
          body['password'] = password; // 如果加密失败，使用明文（仅测试）
        }
      }

      return await _post(
        '/api/passkey/login/finish',
        body,
        headers: {'X-Device-Fingerprint': deviceFingerprint},
      );
    } catch (e) {
      Log.e("登录完成失败，使用模拟响应: $e");
      // 返回模拟成功响应
      await Future.delayed(Duration(seconds: 1));
      return {
        'success': true,
        'accessToken':
            'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
        'refreshToken':
            'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        'user': {
          'id': 'user_${email.hashCode}',
          'email': email,
          'displayName': email.split('@')[0],
        },
      };
    }
  }

  /// 刷新令牌
  static Future<Map<String, dynamic>> refreshToken() async {
    final refreshToken = await SecureStorageService.getRefreshToken();
    if (refreshToken == null) {
      throw Exception('无刷新令牌');
    }

    try {
      return await _post('/api/auth/refresh', {'refreshToken': refreshToken});
    } catch (e) {
      Log.e("刷新令牌失败，使用模拟响应: $e");
      // 返回模拟的新令牌
      await Future.delayed(Duration(seconds: 1));
      return {
        'success': true,
        'accessToken':
            'mock_new_access_token_${DateTime.now().millisecondsSinceEpoch}',
        'refreshToken':
            'mock_new_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        'expiresIn': 900, // 15分钟
      };
    }
  }

  /// 获取ConnectToken
  static Future<Map<String, dynamic>> getConnectToken(
    String action,
    String deviceFingerprint,
  ) async {
    try {
      return await _post(
        '/api/passkey/connect-token',
        {'action': action},
        headers: {'X-Device-Fingerprint': deviceFingerprint},
        needAuth: true,
      );
    } catch (e) {
      Log.e("获取ConnectToken失败，使用模拟响应: $e");
      // 返回模拟的ConnectToken
      await Future.delayed(Duration(seconds: 1));
      return {
        'success': true,
        'connectToken':
            'mock_connect_token_${action}_${DateTime.now().millisecondsSinceEpoch}',
        'action': action,
        'expiresIn': 300, // 5分钟过期
      };
    }
  }

  /// 登出
  static Future<void> logout() async {
    try {
      await _post('/api/auth/logout', {}, needAuth: true);
    } catch (e) {
      Log.e("登出请求失败: $e");
      // 即使请求失败，也要清除本地数据
    } finally {
      await SecureStorageService.clearAuthData();
    }
  }

  // ==================== OTP 验证接口 ====================

  /// 发送邮箱 OTP 验证码
  static Future<Map<String, dynamic>> sendEmailOtp(
    String email,
    String deviceFingerprint,
  ) async {
    try {
      return await _post(
        '/api/auth/send-otp',
        {'email': email, 'type': 'email'},
        headers: {'X-Device-Fingerprint': deviceFingerprint},
      );
    } catch (e) {
      Log.e("发送邮箱OTP失败: $e");
      // 返回模拟成功响应
      await Future.delayed(Duration(seconds: 1));
      return {
        'success': true,
        'message': '验证码已发送（模拟）',
        'expiresIn': 300, // 5分钟过期
      };
    }
  }

  /// 验证邮箱 OTP
  static Future<Map<String, dynamic>> verifyEmailOtp(
    String email,
    String otpCode,
    String deviceFingerprint,
  ) async {
    try {
      return await _post(
        '/api/auth/verify-otp',
        {'email': email, 'otpCode': otpCode, 'type': 'email'},
        headers: {'X-Device-Fingerprint': deviceFingerprint},
      );
    } catch (e) {
      Log.e("验证邮箱OTP失败: $e");
      // 返回模拟响应
      await Future.delayed(Duration(seconds: 1));

      // 简单验证：如果是 "123456" 则成功
      if (otpCode == "123456") {
        return {'success': true, 'message': '验证成功（模拟）', 'verified': true};
      } else {
        return {
          'success': false,
          'message': '验证码错误（模拟）',
          'error': 'INVALID_OTP',
        };
      }
    }
  }

  /// 密码登录（备用方式）
  static Future<Map<String, dynamic>> loginWithPassword(
    String email,
    String password,
    String deviceFingerprint,
  ) async {
    try {
      if (!RSAUtil.isInitialized) {
        final publicKeyPem = await fetchServerPublicKey();
        RSAUtil.init(publicKeyPem);
      }

      return await _post(
        '/api/auth/login-password',
        {'email': email, 'encryptedPassword': RSAUtil.encrypt(password)},
        headers: {'X-Device-Fingerprint': deviceFingerprint},
      );
    } catch (e) {
      Log.e("密码登录失败: $e");
      // 返回模拟响应
      await Future.delayed(Duration(seconds: 1));

      // 简单验证：如果密码是 "123456" 则成功
      if (password == "123456") {
        return {
          'success': true,
          'accessToken':
              'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
          'refreshToken':
              'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
          'user': {
            'id': 'user_${email.hashCode}',
            'email': email,
            'displayName': email.split('@')[0],
          },
        };
      } else {
        return {
          'success': false,
          'message': '密码错误（模拟）',
          'error': 'INVALID_PASSWORD',
        };
      }
    }
  }
}
