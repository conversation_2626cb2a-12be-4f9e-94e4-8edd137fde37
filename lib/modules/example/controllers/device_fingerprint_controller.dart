/*
 * @author: Chend
 * @description: 设备指纹示例控制器
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'package:get/get.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/utils/deveice_utils.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class DeviceFingerprintController extends BaseController {
  // 状态变量
  final fpButtonStatus = ButtonStatus.enable.obs;
  final deviceId = ''.obs;
  final fingerprint = ''.obs;
  final deviceName = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // 初始化时可以获取一些基本信息
    getDeviceName();
  }

  void getDeviceId() async {
    try {
      deviceId.value = await DeviceUtils.getDeviceId() ?? "获取失败";
    } catch (e) {
      deviceId.value = "获取失败: $e";
    }
  }

  void fingerprintAction() async {
    try {
      fpButtonStatus.value = ButtonStatus.loading;
      fingerprint.value = await DeviceUtils.getFingerprint();
      fpButtonStatus.value = ButtonStatus.enable;
    } catch (e) {
      fingerprint.value = "获取失败: $e";
      fpButtonStatus.value = ButtonStatus.enable;
    }
  }

  void getDeviceName() async {
    try {
      deviceName.value = await DeviceUtils.getDeviceName() ?? "未知设备";
    } catch (e) {
      deviceName.value = "获取失败: $e";
    }
  }

  @override
  void loadData() {
    // 可以在这里加载其他数据
  }
}

class DeviceFingerprintBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DeviceFingerprintController());
  }
}
