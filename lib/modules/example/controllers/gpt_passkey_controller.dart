import 'dart:convert';

import 'package:get/get.dart';
import 'package:passkeys/authenticator.dart';
import 'package:passkeys/types.dart';
import 'package:vcb/utils/log_utils.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class LocalRelyingPartyServer {
  Future<Map<String, dynamic>> startPasskeyRegister(
      {required String name}) async {
    await Future.delayed(const Duration(seconds: 1));

    // 生成正确的Base64URL编码challenge（无padding）
    final challengeBytes =
        List.generate(32, (i) => DateTime.now().millisecondsSinceEpoch % 256);
    final challenge = base64Url.encode(challengeBytes).replaceAll('=', '');

    // 生成正确的Base64URL编码用户ID（无padding）
    final userIdBytes =
        utf8.encode('user_${name}_${DateTime.now().millisecondsSinceEpoch}');
    final userId = base64Url.encode(userIdBytes).replaceAll('=', '');

    return {
      'challenge': challenge,
      'rp': {'name': 'VCB Mobile App', 'id': 'com.vcb.finance'},
      'user': {
        'id': userId,
        'name': name,
        'displayName': name,
      },
      'pubKeyCredParams': [
        {'type': 'public-key', 'alg': -7}
      ],
      'timeout': 60000,
      'attestation': 'direct'
    };
  }

  Future<Map<String, dynamic>> startPasskeyLogin({required String name}) async {
    await Future.delayed(const Duration(seconds: 1));

    // 生成正确的Base64URL编码challenge（无padding）
    final challengeBytes =
        List.generate(32, (i) => DateTime.now().millisecondsSinceEpoch % 256);
    final challenge = base64Url.encode(challengeBytes).replaceAll('=', '');

    // 生成正确的Base64URL编码凭据ID（无padding）
    final credentialIdBytes = utf8
        .encode('credential_${name}_${DateTime.now().millisecondsSinceEpoch}');
    final credentialId =
        base64Url.encode(credentialIdBytes).replaceAll('=', '');

    return {
      'challenge': challenge,
      'rpId': 'com.vcb.finance',
      'allowCredentials': [
        {
          'id': credentialId,
          'type': 'public-key',
          'transports': ['internal']
        }
      ],
      'timeout': 60000,
      'userVerification': 'preferred'
    };
  }

  Future<void> finishPasskeyRegister({required dynamic response}) async {
    Log.logPrint("📋 注册完成（模拟后端）: $response");
  }

  Future<void> finishPasskeyLogin({required dynamic response}) async {
    Log.logPrint("📋 登录完成（模拟后端）: $response");
  }
}

class GPTPasskeyController extends GetxController {
  final PasskeyAuthenticator authenticator =
      PasskeyAuthenticator(debugMode: false); // 关闭debug模式避免域名检查
  final LocalRelyingPartyServer rps = LocalRelyingPartyServer();

  var status = ''.obs;
  var loading = false.obs;
  final registerButtonStatus = ButtonStatus.enable.obs;
  final loginButtonStatus = ButtonStatus.enable.obs;

  @override
  void onInit() {
    super.onInit();
    status.value = 'Passkey 控制器已初始化';
  }

  Future<void> signupWithPasskey(String email) async {
    registerButtonStatus.value = ButtonStatus.loading;
    loading.value = true;
    try {
      status.value = '请求注册选项...';
      final options = await rps.startPasskeyRegister(name: email);
      Log.logPrint("注册选项: $options");

      status.value = '唤起系统生物识别弹窗...';

      // 创建正确的RegisterRequestType
      final registerRequest = RegisterRequestType(
        challenge: options['challenge'] as String,
        relyingParty: RelyingPartyType(
          id: (options['rp'] as Map)['id'] as String,
          name: (options['rp'] as Map)['name'] as String,
        ),
        user: UserType(
          id: (options['user'] as Map)['id'] as String,
          name: (options['user'] as Map)['name'] as String,
          displayName: (options['user'] as Map)['displayName'] as String,
        ),
        pubKeyCredParams: (options['pubKeyCredParams'] as List)
            .map((param) => PubKeyCredParamType(
                  type: param['type'] as String,
                  alg: param['alg'] as int,
                ))
            .toList(),
        authSelectionType: AuthenticatorSelectionType(
          authenticatorAttachment: 'platform',
          userVerification: 'required',
          requireResidentKey: false,
          residentKey: '',
        ),
        excludeCredentials: [],
        timeout: options['timeout'] as int?,
        attestation: 'direct',
      );

      // 使用正确构造的RegisterRequestType
      await authenticator.register(registerRequest);
      Log.logPrint("系统 Passkey 注册成功");

      await rps.finishPasskeyRegister(response: 'OK');
      status.value = '注册成功';
    } catch (e) {
      Log.e("注册失败: $e");
      status.value = '注册失败，检查 challenge 格式 / 设备支持';
    } finally {
      registerButtonStatus.value = ButtonStatus.enable;
      loading.value = false;
    }
  }

  Future<void> loginWithPasskey(String email) async {
    loginButtonStatus.value = ButtonStatus.loading;
    loading.value = true;
    try {
      status.value = '请求登录选项...';
      final options = await rps.startPasskeyLogin(name: email);
      Log.logPrint("登录选项: $options");

      status.value = '唤起系统生物识别弹窗...';

      // 暂时简化登录，直接尝试调用
      try {
        await authenticator.authenticate(options as dynamic);
        Log.logPrint("直接调用登录成功");
      } catch (e) {
        Log.e("登录调用失败: $e");
        throw Exception("Passkey登录失败: $e");
      }

      await rps.finishPasskeyLogin(response: 'OK');
      status.value = '登录成功';
    } catch (e) {
      Log.e("登录失败: $e");
      status.value = '登录失败，检查 challenge 格式 / 设备支持';
    } finally {
      loginButtonStatus.value = ButtonStatus.enable;
      loading.value = false;
    }
  }

  // 便捷方法供UI调用
  var email = ''.obs;

  Future<void> quickRegister() async {
    if (email.value.isEmpty) {
      status.value = '请输入邮箱地址';
      return;
    }
    await signupWithPasskey(email.value);
  }

  Future<void> quickLogin() async {
    if (email.value.isEmpty) {
      status.value = '请输入邮箱地址';
      return;
    }
    await loginWithPasskey(email.value);
  }
}

class GPTPasskeyBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GPTPasskeyController());
  }
}
