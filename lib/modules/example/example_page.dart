/*
 * @author: Chen<PERSON>
 * @description: Example 模块主页面
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/example/example_controller.dart';
import 'package:vcb/route/routes.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class ExamplePage extends BaseStatelessWidget<ExampleController> {
  const ExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: 'Example 示例',
        hideLeading: false,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ButtonWidget(
              width: 200,
              text: '通行密钥',
              onPressed: () => Get.toNamed(AppRoutes.passkeyExamplePage),
            ),
            SizedBox(height: 20),
            ButtonWidget(
              width: 200,
              text: 'GPT Passkey方案',
              onPressed: () => Get.toNamed(AppRoutes.gptPasskeyPage),
            ),
            SizedBox(height: 40),
            ButtonWidget(
              width: 200,
              text: 'UI 组件示例',
              onPressed: () => Get.toNamed(AppRoutes.uiComponentsExamplePage),
            ),
            SizedBox(height: 40),
            ButtonWidget(
              width: 200,
              text: '设备指纹示例',
              onPressed: () =>
                  Get.toNamed(AppRoutes.deviceFingerprintExamplePage),
            ),
            SizedBox(height: 40),
            ButtonWidget(
              width: 200,
              text: '网易滑块设备ID示例',
              onPressed: () => Get.toNamed(AppRoutes.neteaseSliderExamplePage),
            ),
            SizedBox(height: 40),
            ButtonWidget(
              width: 200,
              text: 'KYC示例',
              onPressed: () => Get.toNamed(AppRoutes.kycExamplePage),
            ),
          ],
        ),
      ),
    );
  }
}
