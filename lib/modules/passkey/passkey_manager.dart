import 'package:corbado_auth/corbado_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:vcb/app/app_config.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/utils/log_utils.dart';

class PasskeyManager {
  static Future<void> init() async {
    try {
      String projectId = AppConfig.instance.isProduct
          ? Get.getEnvByKey(SDKConstant.PRODUCT_PASSKEY_PROJECT_ID)
          : Get.getEnvByKey(SDKConstant.DEV_PASSKEY_PROJECT_ID);

      Log.logPrint("🚀 初始化CorbadoAuth，项目ID: $projectId");

      final corbadoAuth = CorbadoAuth();
      await corbadoAuth.init(projectId: projectId, debugMode: kDebugMode);

      if (!Get.isRegistered<CorbadoAuth>()) {
        Get.put<CorbadoAuth>(corbadoAuth, permanent: true);
      }
    } catch (e) {
      Log.e('❌ Corbado PasskeyManager 初始化失败: $e');
    }
  }
}
