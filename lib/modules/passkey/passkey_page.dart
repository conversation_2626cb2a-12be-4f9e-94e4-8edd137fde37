import 'package:flutter/material.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/passkey/passkey_controller.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';

/*
 * @author: Chend
 * @description: 通行密钥确认
 * @LastEditTime: 2025-08-05
 */
class PasskeyPage extends BaseStatelessWidget<PasskeyController> {
  const PasskeyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: "passkey", hideLeading: true),
      body: Center(
        child: Text(
          "passkey",
          style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
