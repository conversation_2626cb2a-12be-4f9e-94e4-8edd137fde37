/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-07 15:34:38
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/assets/assets_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';
import 'package:vcb/widgets/image/image_widget.dart';

class AssetsPage extends BaseStatelessWidget<AssetsController> {
  const AssetsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: ID.stringHome.tr,
        hideLeading: true,
        actionWidget: [
          IconButton(
            onPressed: () => Get.toScanner(),
            icon: ImageWidget(
              assetUrl: 'icon_scan',
              width: Get.setImageSize(24),
              height: Get.setImageSize(24),
            ),
          ),
        ],
      ),
      body: Center(
        child: Text(
          ID.stringHome.tr,
          style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
