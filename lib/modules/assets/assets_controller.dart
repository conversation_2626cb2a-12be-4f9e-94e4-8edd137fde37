/*
 * @author: <PERSON><PERSON>
 * @description: 首页控制器
 * @LastEditTime: 2025-08-11 08:59:59
 */

import 'dart:ui';

import 'package:flutter_idensic_mobile_sdk_plugin/flutter_idensic_mobile_sdk_plugin.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/utils/deveice_utils.dart';
import 'package:vcb/utils/log_utils.dart';

class AssetsController extends BaseController {
  RxString fingerprint = ''.obs;
  RxString deviceId = ''.obs;
  @override
  void onInit() {
    super.onInit();
    loadData();
  }

  @override
  void loadData() {
    getDeviceId();
    fingerprintAction();
  }

  void fingerprintAction() async {
    fingerprint.value = await DeviceUtils.getFingerprint();
    Get.showToast(fingerprint.value);
  }

  void getDeviceId() async {
    deviceId.value = await DeviceUtils.getDeviceId() ?? "";
    Get.showToast(fingerprint.value);
  }

  Future<void> kycAction() async {
    /// accessToken过期临时更新 https://docs.sumsub.com/reference/generate-access-token
    final String accessToken =
        '_act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC1hOTNhZTRlYi0xYTk5LTRjZTgtYWMxNS00MzZmZjI3ZmVlYmEtdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2';

    onTokenExpiration() async {
      return Future<String>.delayed(
          Duration(seconds: 2), () => "your new access token");
    }

    onStatusChanged(
        SNSMobileSDKStatus newStatus, SNSMobileSDKStatus prevStatus) {
      Log.logPrint("The SDK status was changed: $prevStatus -> $newStatus");
    }

    final snsMobileSDK = SNSMobileSDK.init(accessToken, onTokenExpiration)
        .withHandlers(onStatusChanged: onStatusChanged)
        .withDebug(true) // set debug mode if required
        .withLocale(Locale("zh"))
        .build();

    final SNSMobileSDKResult result = await snsMobileSDK.launch();

    Log.logPrint("Completed with result: $result");
  }
}

class AssetsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AssetsController());
  }
}
