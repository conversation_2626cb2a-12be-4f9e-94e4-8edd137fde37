// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'language_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LanguageModel _$LanguageModelFromJson(Map<String, dynamic> json) =>
    LanguageModel(
      json['name'] as String,
      json['language'] as String,
      json['country'] as String,
      isSelect: json['isSelect'] as bool? ?? false,
      imagePath: json['imagePath'] as String?,
      requestCountry: json['requestCountry'] as String? ?? '',
      id: (json['id'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$LanguageModelToJson(LanguageModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'language': instance.language,
      'country': instance.country,
      'isSelect': instance.isSelect,
      'imagePath': instance.imagePath,
      'requestCountry': instance.requestCountry,
      'id': instance.id,
    };
