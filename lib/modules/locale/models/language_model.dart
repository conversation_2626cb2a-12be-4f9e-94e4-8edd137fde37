/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-11 17:11:18
 * @LastEditTime: 2025-08-04 11:38:16
 */
import 'package:json_annotation/json_annotation.dart';
import 'package:vcb/database/storage/models/storage_base_model.dart';
import 'package:vcb/modules/locale/locale_controller.dart';

part 'language_model.g.dart';

enum LanguageType { zh, hk, en, ja }

@JsonSerializable()
class LanguageModel extends BaseStorageModel {
  String name = '';
  String language = '';
  String country = '';
  bool isSelect = false;
  String? imagePath;
  String? requestCountry = ""; //后端传参语言
  int id;

  LanguageModel(
    this.name,
    this.language,
    this.country, {
    this.isSelect = false,
    this.imagePath,
    this.requestCountry = '',
    this.id = 0,
  });

  factory LanguageModel.fromJson(Map<String, dynamic> json) =>
      _$LanguageModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$LanguageModelToJson(this);

  LanguageType get type {
    return LanguageType.values[id];
  }

  static LanguageType typeWithCode({String code = '', String? countryCode}) {
    if (code.contains('Hant') ||
        countryCode == 'TW' ||
        countryCode == 'HK' ||
        countryCode == 'MO') {
      return LanguageType.hk;
    } else if (code.contains('Hans') || code.contains('zh')) {
      return LanguageType.zh;
    } else if (code.contains('en')) {
      return LanguageType.en;
    } else if (code.contains('ja')) {
      return LanguageType.ja;
    }
    return LanguageType.en;
  }

  static LanguageModel model(LanguageType type) {
    for (var e in LanguageSource.languageList) {
      if (e.type == type) {
        return e;
      }
    }
    return LanguageModel('English', 'en', 'US',
        imagePath: 'language_usa', requestCountry: "EN", id: 4);
  }

  static LanguageModel originModel(LanguageType type) {
    for (var e in LanguageSource.languageList) {
      if (e.type == type) {
        return e;
      }
    }
    return LanguageModel('English', 'en', 'US',
        imagePath: 'language_usa', requestCountry: "EN", id: 4);
  }
}
