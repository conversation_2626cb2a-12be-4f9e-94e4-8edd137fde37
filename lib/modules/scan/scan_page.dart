/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-12 09:28:51
 * @LastEditTime: 2024-10-16 17:36:01
 */

import 'package:fl_mlkit_scanning/fl_mlkit_scanning.dart';
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateful_widget.dart';
import 'package:vcb/modules/scan/scan_controller.dart';
import 'package:vcb/modules/scan/widgets/scan_animated_view.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';
import 'package:vcb/widgets/image/image_widget.dart';

class ScanPage extends BaseStatefulWidget<ScanController> {
  const ScanPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: const Color.fromRGBO(0, 0, 0, 50),
        appBar: baseAppBar(
          titleWidget: Text(
            ID.scan.tr,
            style: TextStyle(
              color: Get.theme.white,
              fontSize: Get.setFontSize(16),
              fontWeight: FontWeightX.medium,
              fontFamily: Get.setFontFamily(),
            ),
          ),
          isLiaghtStatusBarBrightness: true,
          backgroundColor: Colors.transparent,
          leading: IconButton(
            onPressed: () => Get.back(),
            padding: EdgeInsets.only(left: Get.setWidth(6)),
            icon: ImageWidget(
              assetUrl: "icon_back_light",
              cacheRawData: true,
              shape: BoxShape.circle,
              width: Get.setImageSize(28),
              height: Get.setImageSize(28),
            ),
          ),
        ),
        body: Obx(
          () => Stack(
            fit: StackFit.expand,
            alignment: Alignment.center,
            children: <Widget>[
              _buildScanView(),
              _buildAnimation(),
            ],
          ),
        ));
  }

  SizedBox _buildUninitializedView() => const SizedBox.shrink();

  Visibility _buildAnimation() => Visibility(
        visible: !controller.hideAnimation.value,
        child: ScannerAnimation(
          animation: controller.animationController as Animation<double>,
        ),
      );

  FlMlKitScanning _buildScanView() => FlMlKitScanning(
      frequency: 600,
      resolution: CameraResolution.veryHigh,
      autoScanning: true,
      barcodeFormats: const [BarcodeFormat.all],
      fit: BoxFit.cover,
      uninitialized: _buildUninitializedView(),
      onDataChanged: (AnalysisImageModel data) {
        controller.scanImage(data.barcodes);
      });
}
