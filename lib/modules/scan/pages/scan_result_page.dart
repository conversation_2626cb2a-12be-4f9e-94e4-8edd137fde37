import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';
import 'package:vcb/widgets/button/button_widget.dart';
import 'package:flutter/material.dart';

class ScanResultPage extends BaseStatelessWidget {
  const ScanResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: ID.scanResult.tr,
      ),
      body: SingleChildScrollView(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(Get.arguments[GetArgumentsKey.scanResult] ?? "",
                style: TextStyle(
                    color: Get.theme.textPrimary,
                    fontSize: Get.setFontSize(14),
                    fontFamily: Get.setFontFamily())),
            SizedBox(
              height: Get.setHeight(20),
            ),
            ButtonWidget(
                buttonSize: ButtonSize.full,
                text: ID.copy.tr,
                onPressed: () =>
                    Get.copy(Get.arguments[GetArgumentsKey.scanResult])),
          ],
        ),
      )),
    );
  }
}
