import 'package:fl_mlkit_scanning/fl_mlkit_scanning.dart';
import 'package:flutter/material.dart';
import 'package:vcb/app/app_controller.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/http/apiService/api_service.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/route/routes.dart';

enum ScanAction {
  resultAction, //扫码后立即退出返回扫码结果

  unknown,
}

class ScanController extends BaseController<ApiService>
    with GetTickerProviderStateMixin {
  final bool isChangingCameraLens = false;

  var fullScreenSize = Rx<Size?>(null); // 相机宽高比

  bool isParsingQR = false; //是否正在解析
  int lastCmd = 0;

  RxBool isCameraInitialized = true.obs;
//传进来的钱包对象
  RxBool visiblePage = false.obs; //显示扫码进度

  RxBool hideAnimation = true.obs; //隐藏扫描

  ScanAction scanAction = ScanAction.unknown;
  late AnimationController animationController; //扫描动画

  @override
  void onInit() {
    scanAction =
        Get.arguments?[GetArgumentsKey.scanAction] ?? ScanAction.unknown;

    _initAnimation();

    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
    AppController.resetBrightness();
  }

  @override
  void loadData() {
    _animateScanAnimation();
    hideAnimation.value = false;
  }

  ///或前台返回后台
  @override
  void onPaused() {
    pausePreview();
  }

  ///或后台返回前台回调
  @override
  void onResumed() {
    _resumePreview();
  }

  @override
  void onHidden() {
    pausePreview();
  }

  void restart() {
    isParsingQR = false;
  }

  //扫描动画
  void _initAnimation() {
    animationController = AnimationController(
        duration: const Duration(milliseconds: 1200), vsync: this);
    animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _animateScanAnimation();
      } else if (status == AnimationStatus.dismissed) {
        _animateScanAnimation();
      }
    });
  }

  ///分析二维码图片
  void scanImage(List<Barcode>? barcodes) async {
    if (barcodes == null) {
      return;
    }

    List<String> list =
        barcodes.map((barcode) => barcode.displayValue ?? '').toList();

    String result = list[0];

    if (Get.isEmpty(result)) {
      Get.showToast(ID.emptyData.tr);
      return;
    }

    pausePreview();
    if (scanAction == ScanAction.resultAction) {
      Get.back(result: result);
    } else {
      toScanResult(result);
    }
  }

  void toScanResult(String result) {
    Get.offNamed(AppRoutes.scanResultPage, arguments: {
      GetArgumentsKey.scanResult: result,
    });
  }

  void _animateScanAnimation() {
    animationController.reverse(from: 1.0);
  }

  void _resumePreview() async {
    isParsingQR = false;
    await FlMlKitScanningController().startScanning();
    _animateScanAnimation();
    hideAnimation.value = false;
  }

  void pausePreview() async {
    try {
      isParsingQR = false;
      await FlMlKitScanningController().stopPreview();
      animationController.stop();
      hideAnimation.value = true;
    } catch (_) {}
  }

  @override
  void onClose() {
    animationController.dispose();
    super.onClose();
    AppController.resetBrightness();
  }
}

class ScanBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ScanController());
  }
}
