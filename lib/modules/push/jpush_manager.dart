import 'package:flutter/foundation.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:jpush_google_flutter/jpush_google_flutter.dart';
import 'package:vcb/app/app_config.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';

class JPushManager {
  static final JPushManager instance = JPushManager._internal();
  final JPush _jpush;

  factory JPushManager() {
    return instance;
  }

  JPushManager._internal() : _jpush = JPush();

  Future<void> setup() async {
    _jpush.setup(
      appKey: Get.getEnvByKey(SDKConstant.JpushAppKey), // 你需要替换为自己的appKey
      channel: "the_channel",
      production: AppConfig.instance.isProduct, // 是否为生产环境
      debug: kDebugMode, // 是否打开调试模式
    );

    // 添加处理接收消息的回调函数
    _jpush.addEventHandler(
      onReceiveNotification: (Map<String, dynamic> message) async {
        // 这里是当你收到通知时的业务逻辑
      },
      onOpenNotification: (Map<String, dynamic> message) async {
        // 这里是当用户点击通知打开应用程序时的业务逻辑
      },
      onReceiveMessage: (Map<String, dynamic> message) async {
        // 这里是当用户收到自定义消息时的业务逻辑
      },
    );
  }

  // 以下是公开的用于与JPush交互的方法，通过单例对象调用

  Future<String?> getRegistrationID() async {
    return await _jpush.getRegistrationID();
  }

  Future<void> sendLocalNotification(LocalNotification notification) async {
    await _jpush.sendLocalNotification(notification);
  }
}
