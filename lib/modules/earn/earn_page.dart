import 'package:flutter/material.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/earn/earn_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';

/*
 * @author: Chend
 * @description: 理财
 * @LastEditTime: 2025-08-05
 */
class EarnPage extends BaseStatelessWidget<EarnController> {
  const EarnPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringEarn.tr, hideLeading: true),
      body: Center(
        child: Text(
          ID.stringEarn.tr,
          style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
