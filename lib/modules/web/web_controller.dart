/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2025-02-26 16:59:59
 */

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/database/db_provider.dart';
import 'package:vcb/modules/web/dialog/web_action_dialog.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/utils/file_utils.dart';
import 'package:vcb/utils/log_utils.dart';
import 'package:vcb/widgets/button/button_widget.dart';
import 'package:vcb/widgets/text_field/text_field_widget.dart';

class WebController extends BaseController<AppDatabase> {
  var title = ''.obs;
  var url = ''.obs;

  ///当前页面是否关闭
  var isAlive = false.obs;

  bool isInitLoad = false;

  var showTitle = true.obs;

  var isLoaded = false.obs; // 网页是否加载完成

  var onLoadError = false.obs;
  var progress = 0.0.obs; // 加载进度

  InAppWebViewController? webViewController; // WebView 控制器

  @override
  void onInit() {
    super.onInit();
    Log.r("onInit");
    url.value = Get.arguments?[GetArgumentsKey.url] ?? '';
    title.value = Get.arguments?[GetArgumentsKey.title] ?? '';
    showTitle.value = Get.arguments?[GetArgumentsKey.showTitle] ?? true;
    url.value = FileUtils.formatUrl(url.value);
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() async {}

  // 处理加载进度
  void updateProgress(double newProgress) {
    progress.value = newProgress;
  }

  // 加载特定的 URL
  void loadUrl() {
    onLoadError.value = false;
    isLoaded.value = false;
    progress.value = 0.0;
    webViewController!.loadUrl(urlRequest: URLRequest(url: WebUri(url.value)));
  }

  // 处理网页加载完成
  void onWebViewLoaded() {
    isLoaded.value = true;
  }

  // 重新加载网页
  void reload() {
    if (onLoadError.value) {
      onLoadError.value = false;
      isLoaded.value = false;
      progress.value = 0.0;
    } else {
      onLoadError.value = false;
      isLoaded.value = false;
      progress.value = 0.0;
      if (!onLoadError.value) {
        webViewController!.reload();
      }
    }
  }

  // 重新加载网页
  void goBack() async {
    if (webViewController == null) {
      Get.back();
    } else {
      if (!onLoadError.value && await webViewController!.canGoBack()) {
        webViewController!.goBack();
      } else {
        Get.back();
      }
    }
  }

  // 复制网页
  void actionCoinLink() {
    Get.back();
    Get.copy(url.value);
  }

  // 分享网页
  void actionShare() {
    Get.back();
    Get.share(url.value);
  }

  // 浏览器打开
  void actionOpenInBrowser() {
    Get.back();
    Get.openLink(url.value);
  }

  void showActionDialog() {
    WebAcitonDialog(webController: this).showBottomSheet();
  }

  Future<void> onLoadStop(
      InAppWebViewController inAppWebViewController, Uri? uri) async {
    Log.r("onLoadStop=");

    if (onLoadError.value) {
      return;
    }

    isInitLoad = false;

    Future.delayed(const Duration(milliseconds: 300), () {
      isLoaded.value = true;
    });
  }

  Future<void> onReceivedError(Uri? uri, WebResourceError error) async {
    Log.r("onReceivedError=$error");

    final errorTypesToHandle = {
      WebResourceErrorType.CANNOT_CONNECT_TO_HOST,
      WebResourceErrorType.CONNECTION_ABORTED,
      WebResourceErrorType.NETWORK_CONNECTION_LOST,
      WebResourceErrorType.TIMEOUT
    };

    onLoadError.value = errorTypesToHandle.contains(error.type) &&
        (error.type != WebResourceErrorType.TIMEOUT || isInitLoad);
  }

  Future<void> onReceivedHttpError(Uri? uri, WebResourceResponse error) async {
    Log.r("onReceivedHttpError=$error");
    if (error.statusCode! == 401) {
      onLoadError.value = true;
    }
  }

  Future<HttpAuthResponse> httpAuth(InAppWebViewController controller) async {
    final url = await controller.getUrl();
    final host = url?.host;

    String? userName;
    String? password;

    await Get.showBottomSheet(
        title: "${ID.stringLogin.tr} $host ?? ${this.url.value}",
        disableBack: false,
        enableDrag: false,
        barrierDismissible: false,
        bodyWidget: Padding(
          padding: EdgeInsets.only(
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            top: Get.setPaddingSize(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                ID.stringUserName.tr,
                style: textSecondary14Regular,
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              TextFieldWidget(
                onValueChanged: (value) => userName = value,
              ),
              SizedBox(height: Get.setPaddingSize(24)),
              Text(
                ID.stringPassword.tr,
                style: textSecondary14Regular,
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              TextFieldWidget(
                obscureText: true,
                onValueChanged: (value) => password = value,
              ),
              SizedBox(height: Get.setPaddingSize(24)),
              ButtonWidget(
                width: Get.width,
                text: ID.stringLogin.tr,
                onPressed: () {
                  Get.back();
                },
              ),
            ],
          ),
        ));

    if (!Get.isEmptyString(userName) && !Get.isEmptyString(password)) {
      return HttpAuthResponse(
        action: HttpAuthResponseAction.PROCEED,
        username: userName!,
        password: password!,
      );
    }

    return HttpAuthResponse(action: HttpAuthResponseAction.CANCEL);
  }
}

class WebBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => WebController());
  }
}
