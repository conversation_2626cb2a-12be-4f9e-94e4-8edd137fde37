/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-09-23 16:17:11
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/web/web_controller.dart';
import 'package:vcb/res/resource.dart';

class WebProgressWidget extends BaseStatelessWidget<WebController> {
  const WebProgressWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() => controller.isLoaded.value
        ? const SizedBox.shrink()
        : SizedBox(
            width: Get.width,
            height: Get.setHeight(2),
            child: LinearProgressIndicator(
              minHeight: Get.setHeight(2),
              value: controller.progress.value,
              backgroundColor: Get.theme.bgColor,
              valueColor: AlwaysStoppedAnimation(Get.theme.primary),
              borderRadius: BorderRadius.circular(Get.setRadius(1)),
            ),
          ));
  }
}
