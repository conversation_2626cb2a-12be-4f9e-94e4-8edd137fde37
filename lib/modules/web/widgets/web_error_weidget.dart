/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-09-29 17:53:48
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/web/web_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/button/outlined_button_widget.dart';
import 'package:vcb/widgets/image/image_widget.dart';

class WebErrorWidget extends BaseStatelessWidget<WebController> {
  const WebErrorWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
        child: SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ImageWidget(
            assetUrl: "ic_no_data",
            width: Get.setImageSize(88),
            height: Get.setImageSize(88),
          ),
          SizedBox(
            height: Get.setHeight(14),
          ),
          Text(ID.stingCannotAccessThisWebsite.tr,
              style: TextStyle(
                color: Get.theme.textSecondary,
                fontSize: Get.setFontSize(14),
                fontFamily: Get.setFontFamily(),
                fontWeight: FontWeightX.regular,
              )),
          SizedBox(
            height: Get.setHeight(8),
          ),
          Text(ID.stingCannotAccessThisWebsiteVPN.tr,
              style: TextStyle(
                color: Get.theme.textSecondary,
                fontSize: Get.setFontSize(14),
                fontFamily: Get.setFontFamily(),
                fontWeight: FontWeightX.regular,
              )),
          SizedBox(
            height: Get.setHeight(8),
          ),
          OutlinedButtonWidget(
              height: Get.setHeight(32),
              text: ID.reload.tr,
              textStyle: TextStyle(
                color: Get.theme.textSecondary,
                fontSize: Get.setFontSize(14),
                fontFamily: Get.setFontFamily(),
                fontWeight: FontWeightX.regular,
              ),
              onPressed: () => controller.reload()),
        ],
      ),
    ));
  }
}
