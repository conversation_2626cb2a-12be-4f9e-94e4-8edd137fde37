/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-10-01 01:33:43
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/web/web_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/image/image_widget.dart';

class WebStartWidget extends BaseStatelessWidget<WebController> {
  const WebStartWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
        () => (controller.progress.value >= 0.5 || controller.onLoadError.value)
            ? const SizedBox.shrink()
            : Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 60),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 14),
                          child: ImageWidget(
                            assetUrl: "icon_dapp_default_logo",
                            width: Get.setImageSize(60),
                            height: Get.setImageSize(60),
                          ),
                        ),
                        Text(controller.url.value,
                            maxLines: 1,
                            style: TextStyle(
                              color: Get.theme.textPrimary,
                              fontSize: Get.setFontSize(18),
                              fontFamily: Get.setFontFamily(),
                              overflow: TextOverflow.ellipsis,
                              fontWeight: FontWeightX.regular,
                            )),
                        SizedBox(
                          height: Get.setHeight(8),
                        ),
                        Text(ID.stringRedirecting.tr,
                            style: TextStyle(
                              color: Get.theme.textPrimary,
                              fontSize: Get.setFontSize(14),
                              fontFamily: Get.setFontFamily(),
                              fontWeight: FontWeightX.regular,
                            )),
                      ],
                    ),
                  ),
                ),
              ));
  }
}
