/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-09-14 09:44:31
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/web/web_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/image/image_widget.dart';

class WebActionWidget extends BaseStatelessWidget<WebController> {
  const WebActionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Get.setRadius(18)),
        border: Border.all(
          color: Get.theme.colorECECEC,
          width: 0.5,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => controller.showActionDialog(),
            child: Container(
              color: Colors.transparent,
              padding: EdgeInsets.only(
                  top: Get.setPaddingSize(6),
                  bottom: Get.setPaddingSize(6),
                  left: Get.setPaddingSize(12),
                  right: Get.setPaddingSize(4)),
              child: ImageWidget(
                assetUrl: "icon_dapp_more",
                width: Get.setImageSize(16),
                height: Get.setImageSize(16),
              ),
            ),
          ),
          Container(
              margin: EdgeInsets.symmetric(horizontal: Get.setFontSize(4)),
              width: Get.setPaddingSize(0.5),
              height: Get.setPaddingSize(8),
              color: Get.theme.colorD3D3D3),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => Get.back(),
            child: Container(
              color: Colors.transparent,
              padding: EdgeInsets.only(
                  top: Get.setPaddingSize(6),
                  bottom: Get.setPaddingSize(6),
                  left: Get.setPaddingSize(4),
                  right: Get.setPaddingSize(12)),
              child: ImageWidget(
                assetUrl: "icon_dapp_close",
                width: Get.setImageSize(16),
                height: Get.setImageSize(16),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
