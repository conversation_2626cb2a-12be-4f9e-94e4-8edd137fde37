import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/web/web_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/image/image_widget.dart';

class WebAcitonDialog extends BaseStatelessWidget {
  final WebController? webController;

  const WebAcitonDialog({super.key, this.webController});

  void showBottomSheet() {
    Get.showBottomSheet(
      bodyWidget: this,
      paddingBottom: 0,
      bgColor: Get.theme.colorF9F9F9,
      customHeadWidget: headerWidget(),
    );
  }

  Padding headerWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(16), vertical: Get.setPaddingSize(12)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Text.rich(
                maxLines: 1,
                TextSpan(children: <InlineSpan>[
                  TextSpan(
                    text: webController!.title.value,
                    style: TextStyle(
                        fontSize: Get.setFontSize(18),
                        fontFamily: Get.setNumberFontFamily(),
                        fontWeight: FontWeightX.medium,
                        color: Get.theme.textPrimary,
                        overflow: TextOverflow.ellipsis),
                  ),
                ])),
          ),
          const SizedBox(
            width: 10,
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              Get.back();
            },
            child: Container(
              color: Get.theme.bgColor,
              child: ImageWidget(
                assetUrl: "icon_exit",
                width: Get.setWidth(24),
                height: Get.setHeight(24),
              ),
            ),
          )
        ],
      ),
    );
  }

  @override
  build(BuildContext context) {
    return Obx(
      () => Padding(
        padding: EdgeInsets.fromLTRB(Get.setFontSize(8), Get.setFontSize(16),
            Get.setFontSize(8), Get.setFontSize(32)),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            _actionItem("icon_action_copy", ID.copyLink.tr,
                onTap: () => webController!.actionCoinLink()),
            _actionItem("icon_refresh", ID.refresh.tr, onTap: () {
              Get.back();
              webController!.reload();
            }),
            _actionItem("icon_browser", ID.penInBrowser.tr,
                onTap: () => webController!.actionOpenInBrowser()),
            _actionItem("icon_share", ID.share.tr,
                onTap: () => webController!.actionShare()),
          ]),
          const Spacer(),
          const Spacer()
        ]),
      ),
    );
  }

  Widget _actionItem(String? assetUrl, String? title,
          {bool visible = true, GestureTapCallback? onTap}) =>
      !visible
          ? const Spacer()
          : Expanded(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: onTap,
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.symmetric(
                          horizontal: Get.setPaddingSize(18)),
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(Get.setRadius(12)),
                        color: Get.theme.bgColor,
                      ),
                      child: ImageWidget(
                        assetUrl: assetUrl,
                        width: Get.setImageSize(28),
                        height: Get.setImageSize(28),
                        fit: BoxFit.contain,
                      ),
                    ),
                    SizedBox(
                      height: Get.setHeight(8),
                    ),
                    Text(
                      title ?? '',
                      style: TextStyle(
                          fontSize: Get.setFontSize(12),
                          fontFamily: Get.setFontFamily(),
                          color: Get.theme.textPrimary,
                          overflow: TextOverflow.ellipsis),
                    )
                  ],
                ),
              ),
            );
}
