/*
 * @author: <PERSON><PERSON>
 * @description: 注册页面
 * @LastEditTime: 2025-01-XX XX:XX:XX
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/auth/register/register_controller.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';

class RegisterPage extends BaseStatelessWidget<RegisterController> {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: '注册', hideLeading: true),
      body: const Center(
        child: Text(
          '注册',
          style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
