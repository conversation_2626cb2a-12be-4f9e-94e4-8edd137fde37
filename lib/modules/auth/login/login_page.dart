/*
 * @author: <PERSON><PERSON>
 * @description: 登录页面
 * @LastEditTime: 2025-01-XX XX:XX:XX
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/auth/login/login_controller.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';

class LoginPage extends BaseStatelessWidget<LoginController> {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: '登录', hideLeading: true),
      body: const Center(
        child: Text(
          '登录',
          style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
