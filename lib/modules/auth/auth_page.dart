/*
 * @author: <PERSON><PERSON>
 * @description:  Passkey 鉴权页面
 * @LastEditTime: 2025-01-XX XX:XX:XX
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/auth/auth_controller.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';

class AuthPage extends BaseStatelessWidget<AuthController> {
  const AuthPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: 'Auth', hideLeading: true),
      body: const Center(
        child: Text(
          'Auth',
          style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
