/*
 * @author: Chend
 * @description: 认证控制器 - 统一管理用户认证状态和登录逻辑
 * @LastEditTime: 2025-08-09 XX:XX:XX
 */

import 'dart:io';

import 'package:corbado_auth/corbado_auth.dart';
import 'package:get/get.dart';
import 'package:passkeys/authenticator.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/database/storage/secure_storage_service.dart';
import 'package:vcb/modules/example/services/http_api_service.dart';
import 'package:vcb/route/routes.dart';
import 'package:vcb/utils/log_utils.dart';

/// 认证控制器
///
/// 职责：
/// - 管理用户登录状态
/// - 处理登录/登出逻辑
/// - 令牌管理和自动刷新
/// - 认证状态变化通知
///
/// 使用场景：
/// - 应用启动时检查登录状态
/// - 用户登录/登出操作
/// - API请求前的认证检查
/// - 令牌过期时的自动刷新
class AuthController extends BaseController {
  static AuthController get to => Get.find<AuthController>();

  @override
  void loadData() {}

  // ==================== 状态变量 ====================

  /// 是否已登录
  final isLoggedIn = false.obs;

  /// 当前用户信息
  final currentUser = Rxn<Map<String, dynamic>>();

  /// 访问令牌
  final accessToken = ''.obs;

  /// 是否正在刷新令牌
  final isRefreshing = false.obs;

  /// 认证状态消息
  final authMessage = ''.obs;

  // ==================== 生命周期 ====================

  @override
  void onInit() {
    super.onInit();
    _initAuthState();
    Log.logPrint("AuthController 初始化完成");
  }

  /// 初始化认证状态
  Future<void> _initAuthState() async {
    try {
      // 从安全存储中恢复认证状态
      await _loadStoredAuthData();

      // 检查令牌有效性
      if (accessToken.value.isNotEmpty) {
        await checkTokenValidity();
      }

      Log.logPrint("认证状态初始化完成，登录状态: ${isLoggedIn.value}");
    } catch (e) {
      Log.e("认证状态初始化失败: $e");
      await logout();
    }
  }

  /// 检查设备是否支持Passkey
  Future<bool> isPasskeySupported() async {
    try {
      Log.logPrint("🔍 检查设备Passkey支持");

      // 1. 检查CorbadoAuth是否已初始化
      try {
        final corbadoAuth = Get.find<CorbadoAuth>();
        final projectId = corbadoAuth.projectId;
        if (projectId.isEmpty) {
          Log.logPrint("❌ CorbadoAuth未初始化");
          return false;
        }
      } catch (e) {
        Log.e("❌ CorbadoAuth不可用: $e");
        return false;
      }

      // 2. 创建PasskeyAuthenticator实例检查支持
      try {
        final authenticator = PasskeyAuthenticator(debugMode: false);

        // 获取设备可用性信息
        final availability = authenticator.getAvailability();

        if (Platform.isAndroid) {
          final androidAvailability = await availability.android();
          final isSupported = androidAvailability.isNative &&
              androidAvailability
                  .isUserVerifyingPlatformAuthenticatorAvailable!;
          Log.logPrint("Android Passkey支持: $isSupported");
          return isSupported;
        } else if (Platform.isIOS) {
          final iosAvailability = await availability.iOS();
          final isSupported =
              iosAvailability.isNative && iosAvailability.hasBiometrics;
          Log.logPrint("iOS Passkey支持: $isSupported");
          return isSupported;
        }

        return false;
      } catch (e) {
        Log.e("❌ PasskeyAuthenticator检查失败: $e");
        return false;
      }
    } catch (e) {
      Log.e("❌ 检查Passkey支持失败: $e");
      return false;
    }
  }

  /// 从存储中加载认证数据
  Future<void> _loadStoredAuthData() async {
    try {
      // 加载令牌
      final token = await SecureStorageService.getAccessToken();
      if (token != null && token.isNotEmpty) {
        accessToken.value = token;
      }

      // 加载用户信息
      final userInfo = SecureStorageService.getUserInfo();
      if (userInfo != null) {
        currentUser.value = userInfo;
        isLoggedIn.value = true;
      }

      Log.logPrint("认证数据加载完成");
    } catch (e) {
      Log.e("加载认证数据失败: $e");
    }
  }

  // ==================== 认证检查 ====================

  /// 检查是否已登录
  bool get isAuthenticated => isLoggedIn.value && accessToken.value.isNotEmpty;

  /// 检查令牌有效性
  Future<bool> checkTokenValidity() async {
    if (accessToken.value.isEmpty) {
      await logout();
      return false;
    }

    try {
      final result = await HttpApiService.checkToken();
      if (result['success'] == true) {
        isLoggedIn.value = true;
        authMessage.value = '认证有效';
        return true;
      } else {
        // 令牌无效，尝试刷新
        return await _attemptTokenRefresh();
      }
    } catch (e) {
      Log.e("检查令牌有效性失败: $e");
      await logout();
      return false;
    }
  }

  /// 尝试刷新令牌
  Future<bool> _attemptTokenRefresh() async {
    if (isRefreshing.value) return false;

    try {
      isRefreshing.value = true;
      authMessage.value = '正在刷新认证...';

      ///TODO这里后端代码

      final result = await HttpApiService.refreshToken();
      if (result['accessToken'] != null) {
        // 保存新令牌
        await _saveAuthData(
          accessToken: result['accessToken'],
          refreshToken: result['refreshToken'],
        );

        authMessage.value = '认证刷新成功';
        return true;
      } else {
        await logout();
        return false;
      }
    } catch (e) {
      Log.e("刷新令牌失败: $e");
      await logout();
      return false;
    } finally {
      isRefreshing.value = false;
    }
  }

  // ==================== 登录/登出 ====================

  /// 处理登录成功
  ///
  /// [authData] 认证数据，包含令牌和用户信息
  Future<void> handleLoginSuccess(Map<String, dynamic> authData) async {
    try {
      // 保存认证数据
      await _saveAuthData(
        accessToken: authData['accessToken'],
        refreshToken: authData['refreshToken'],
        userInfo: authData['user'],
      );

      // 更新状态
      isLoggedIn.value = true;
      authMessage.value = '登录成功';

      Log.logPrint("用户登录成功: ${currentUser.value?['email']}");

      // 导航到主页
      Get.offAllNamed(AppRoutes.mainPage);
    } catch (e) {
      Log.e("处理登录成功失败: $e");
      authMessage.value = '登录处理失败';
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      authMessage.value = '正在登出...';

      // 调用后端登出接口
      try {
        await HttpApiService.logout();
      } catch (e) {
        Log.e("后端登出失败: $e");
        // 即使后端失败也要清除本地数据
      }

      // 清除本地认证数据
      await SecureStorageService.clearAuthData();

      // 重置状态
      isLoggedIn.value = false;
      currentUser.value = null;
      accessToken.value = '';
      authMessage.value = '已登出';

      Log.logPrint("用户已登出");

      // 导航到登录页
      Get.offAllNamed(AppRoutes.loanPage);
    } catch (e) {
      Log.e("登出失败: $e");
      authMessage.value = '登出失败';
    }
  }

  /// 强制登出（不调用后端）
  Future<void> forceLogout() async {
    await SecureStorageService.clearAuthData();
    isLoggedIn.value = false;
    currentUser.value = null;
    accessToken.value = '';
    authMessage.value = '强制登出';
    Get.offAllNamed(AppRoutes.loanPage);
  }

  // ==================== 私有方法 ====================

  /// 保存认证数据
  Future<void> _saveAuthData({
    String? accessToken,
    String? refreshToken,
    Map<String, dynamic>? userInfo,
  }) async {
    if (accessToken != null) {
      await SecureStorageService.saveAccessToken(accessToken);
      this.accessToken.value = accessToken;
    }

    if (refreshToken != null) {
      await SecureStorageService.saveRefreshToken(refreshToken);
    }

    if (userInfo != null) {
      SecureStorageService.saveUserInfo(userInfo);
      currentUser.value = userInfo;
    }
  }

  // ==================== 工具方法 ====================

  /// 获取当前用户邮箱
  String? get userEmail => currentUser.value?['email'];

  /// 获取当前用户显示名称
  String? get userDisplayName => currentUser.value?['displayName'];

  /// 获取当前用户ID
  String? get userId => currentUser.value?['id'];

  /// 需要登录时的处理
  void requireLogin({String? message}) {
    authMessage.value = message ?? '请先登录';
    Get.toNamed(AppRoutes.loanPage);
  }
}
