import 'package:flutter/material.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/profile/profile_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';

/*
 * @author: Chend
 * @description: 个人中心
 * @LastEditTime: 2025-08-05
 */
class ProfilePage extends BaseStatelessWidget<ProfileController> {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringProfile.tr, hideLeading: true),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              ID.stringProfile.tr,
              style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
            ),
            const Sized<PERSON>ox(height: 40),
            ElevatedButton(
              onPressed: () => controller.goToLogin(),
              child: Text(ID.stringLogin.tr),
            ),
            const Sized<PERSON>ox(height: 20),
            ElevatedButton(
              onPressed: () => controller.goToRegister(),
              child: Text(ID.stringRegister.tr),
            ),
          ],
        ),
      ),
    );
  }
}
