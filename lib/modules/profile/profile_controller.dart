/*
 * @author: Chend
 * @description: 我的控制器
 * @LastEditTime: 2025-01-XX XX:XX:XX
 */
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/route/routes.dart';

class ProfileController extends BaseController {
  @override
  void loadData() {}

  void goToLogin() {
    Get.toNamed(AppRoutes.loginPage);
  }

  void goToRegister() {
    Get.toNamed(AppRoutes.registerPage);
  }
}

class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ProfileController());
  }
}
