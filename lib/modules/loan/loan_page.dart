import 'package:flutter/material.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/loan/loan_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';

/*
 * @author: Chend
 * @description: 借贷
 * @LastEditTime: 2025-08-05
 */
class LoanPage extends BaseStatelessWidget<LoanController> {
  const LoanPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringLoan.tr, hideLeading: true),
      body: Center(
        child: Text(
          ID.stringLoan.tr,
          style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
