/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 11:40:44
 */

import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/res/resource.dart';

class GuideController extends BaseController {
  @override
  void loadData() async {}
}

class GuideBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GuideController());
  }
}
