/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2025-08-04 14:51:35
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/splash/controllers/guide_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/appbar/base_app_bar.dart';

class GuidePage extends BaseStatelessWidget<GuideController> {
  const GuidePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(hideLeading: true),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [Center(child: Text('引导页'))]),
        ));
  }
}
