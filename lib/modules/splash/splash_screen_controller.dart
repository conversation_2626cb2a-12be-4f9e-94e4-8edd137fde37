/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 14:50:11
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/modules/assets/assets_controller.dart';
import 'package:vcb/modules/earn/earn_controller.dart';
import 'package:vcb/modules/example/example_controller.dart';
import 'package:vcb/modules/loan/loan_controller.dart';
import 'package:vcb/modules/profile/profile_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/route/routes.dart';

class SplashScreenController extends BaseController {
  @override
  void loadData() {}

  void delayedAction() {
    Future.delayed(const Duration(seconds: 2)).then((value) async {
      launch();
    });
    if (GetPlatform.isAndroid) {
      SystemUiOverlayStyle style = const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      );
      SystemChrome.setSystemUIOverlayStyle(style);
    }
  }
}

void launch() {
  Get.offAllNamed(AppRoutes.mainPage);
}

void toMain() {
  Get.offAllNamed(AppRoutes.mainPage);
}

class SplashScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SplashScreenController());
    Get.lazyPut(() => AssetsController());
    Get.lazyPut(() => EarnController());
    Get.lazyPut(() => LoanController());
    Get.lazyPut(() => ProfileController());
    Get.lazyPut(() => ExampleController());
  }
}
