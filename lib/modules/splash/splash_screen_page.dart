/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-20 13:51:38
 */

import 'package:flutter/material.dart';
import 'package:vcb/base/state/base_stateless_widget.dart';
import 'package:vcb/modules/splash/splash_screen_controller.dart';
import 'package:vcb/widgets/image/image_widget.dart';

class SplashScreenPage extends BaseStatelessWidget<SplashScreenController> {
  const SplashScreenPage({super.key});

  @override
  Widget build(BuildContext context) {
    controller.delayedAction();
    return const Scaffold(
      body: Center(
        child: ImageWidget(
          width: 142,
          height: 142,
          assetUrl: "icon_screen_logo",
        ),
      ),
    );
  }
}
