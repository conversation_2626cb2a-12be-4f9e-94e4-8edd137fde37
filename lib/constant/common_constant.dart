/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 09:18:48
 * @LastEditTime: 2025-08-08 10:56:50
 */

/// 通用常量
class CommonConstant {
  // ================== App 基础 ==================
  static const String appName = "appname";
  static const String mobileId = "mobileId";
  static const String currency = "currency";

  // ================== 资产显示 ==================
  static const String hideAssetsStr = "******"; // 隐藏资产
  static const String zeroAssets = "0.00"; // 资产为零
  static const String emptyTotalAssets = "--.--"; // 总资产为空
  static const String emptyAssets = "--"; // 单项资产为空

  // ================== 资源路径 ==================
  static const String assetsPath = "assets/images/";
  static const String imagePng = ".png";
  static const String imageJpeg = ".jpeg";

  // ================== 其它 ==================
  static const double brightness = 0.5;
}

class StorageKey {
  /// 保存多语言key
  static const String language = 'language';

  /// 首次启动
  static const String fristLaunch = "fristLaunch";
}

class GetArgumentsKey {
  /// web url
  static const String url = 'url';

  /// web title
  static const String title = 'title';

  /// showTitle
  static const String showTitle = 'showTitle';

  /// 拒绝申请权限
  static const String kHaveDenied = 'kHaveDenied';

  ///扫描动作
  static const String scanAction = 'scanAction';

  ///扫描结果
  static const String scanResult = 'scanResult';
}

/// @description: GetBuilder Id key
class GetKey {
  /// blockBaseUrl
  static const String blockBaseUrl = 'blockBaseUrl';

  /// apiBaseUrl
  static const String apiBaseUrl = 'apiBaseUrl';
}

class Flavor {
  /// Android 官网 渠道
  static const String official = "official";

  /// Android GooglePlay 渠道
  static const String googlePlay = "googlePlay";

  /// ios AppStrore
  static const String appStore = "appStore";

  /// ios testFlight
  static const String testFlight = "testFlight";
}

/// 官网相关或者协议相关URL
class AgreementUrl {
  ///官网
  static const String official = 'https://www.coinbagbit.com/';

  /// 关于 URL
  static const String aboutUrl = 'https://www.coinbagbit.com/';

  /// 用户协议 URL
  static const String userArgumentZH = 'https://www.coinbagbit.com/zh/privacy/';
  static const String userArgumentEN = 'https://www.coinbagbit.com/en/privacy/';

  ///官网
  static const String marketUrl = "https://www.coinbagbit.com/";

  ///iOS App Store 链接
  static const String appStoreUrl =
      'https://apps.apple.com/us/app/coinbagbit/id6479732222';

  /// Android Google Play 链接
  static const String playStoreUrl =
      'https://play.google.com/store/apps/details?id=com.coin.bag';
}

///实际key配置在 assets/env/.env
class SDKConstant {
  /// Bugly
  static String BuglyAndroidAppId = "BuglyAndroidAppId";
  static String BuglyIOSAppId = "BuglyIOSAppId";

  /// 极光推送
  static String JpushAppKey = "JpushAppKey";

  /// 设备指纹
  static String FingerprintApiKey = "FingerprintApiKey";
  static String REGION = "REGION";
  static String SCRIPT_URL_PATTERN = "SCRIPT_URL_PATTERN";
  static String ENDPOINT = "ENDPOINT";
  static String ENDPOINTFALLBACKS = "ENDPOINTFALLBACKS";

  /// 网易易盾
  static String CaptchaId = "CaptchaId";

  /// corbado通行密钥
  /// ===== 开发环境 =====
  static String DEV_PASSKEY_PROJECT_ID = "DEV_PASSKEY_PROJECT_ID";
  static String DEV_FRONTEND_URL = "DEV_FRONTEND_URL";
  static String DEV_BACKEND_URL = "DEV_BACKEND_URL";

  ///===== 生产环境 =====
  static String PRODUCT_PASSKEY_PROJECT_ID = "PRODUCT_PASSKEY_PROJECT_ID";
  static String PRODUCT_BACKEND_URL = "PRODUCT_BACKEND_URL";
  static String PRODUCT_FRONTEND_URL = "PRODUCT_FRONTEND_URL";
}
