class ApiConfig {
  // ================== Config Base URLs ==================

  ///动态环境api config base url
  static String configBaseUrl = "https://config.hengshengjia.com/config";

  /// 生产环境API Config BaseUrl
  static const String productApiConfigUrl =
      "https://config.hengshengjia.com/config";

  /// 测试环境 api Config  Test BaseUrl
  static const String devApiConfigUrl =
      "https://config.hengshengjia.com/config";

  /// mock环境API Config BaseUrl
  static const String mockApiConfigUrl =
      "https://config.hengshengjia.com/config";

  /// 预生产环境 apiConfig
  static const String previewApiConfigUrl =
      "https://config.hengshengjia.com/config";

  /// 预生产环境 apiConfig
  static const String degbugApiConfigUrl =
      "https://config.hengshengjia.com/config";

  // ================== API Base URLs ==================

  ///动态环境Base URL
  static String baseUrl = "https://api.coinbagbit.com";

  ///生产环境Base URL
  static const String productBaseUrl = "https://api.coinbagbit.com";

  ///测试环境Base URL
  static const String devBaseUrl = "http://**************:23130";

  //mock环境Base URL
  static const String mockBaseUrl = "https://api.coinbagbit.com";

  ///预生产环境Base URL
  static const String previewBaseUrl = "https://api.coinbagbit.com";

  /// 该 URL 适用于本地调试，通常指向服务在本地时，未上线时。
  static const String debugBaseUrl = "http://localhost:8080"; // 可根据实际情况修改
}

class APIConstant {
  /// 成功 code
  static const int responseCode = 0;

  /// JWT 过期，需要刷新或重新登录
  static const int codeJwtExpired = 10001;

  /// 未登录（无 token 或 token 无效）
  static const int codeNotLoggedIn = 10002;

  /// 需要滑块验证（行为验证码触发）
  static const int codeNeedCaptcha = 10003;

  /// 账号已锁定
  static const int codeAccountLocked = 10004;

  /// 参数错误
  static const int codeParamError = 20001;

  /// 服务器内部错误
  static const int codeServerError = 50000;

  /// 成功 code V2
  static const int responseCodeV2 = 0;

  /// 忽略拦截器
  static const String ignoreIntercep = "ignoreIntercep";
}
