/*
 * @description: 移动端API文档整理
 * @Author: wangdognshenng
 * @Date: 2024-01-09 18:16:49
 * @LastEditTime: 2025-04-02 15:42:02
 */

class API {
  // ================== 资产 ==================

  /// 获取币种行情
  static const String getMarketPriceList = "/app/market/list";

  /// 获取美元汇率和USDT单价
  static const String getUSDPrices = "/app/index/listcoinPrices?coins=USDT";

// ================== 理财 ==================

  ///理财首页推荐列表
  static const String getDappInfo = '/app/dapp/getDappInfo';

// ================== 借贷 ==================

// ================== Auth ==================
  ///登录
  static const String getLogin = '/app/user/quickLogin';

  // 忘记密码验证码
  static const String getCodeForget = '/app/common/login/sendMsg';

  // 获取验证码
  static const String getCode = '/app/common/register/sendMsg';

  // 注册
  static const String register = '/app/common/checkIdentCode';

  // 设置密码
  static const String passwordPhone = '/app/user/registerByPhone';

  // 设置密码
  static const String passwordEamil = '/app/user/registerByEmail';

  // 忘记密码
  static const String forgetPassword = '/app/user/resetLoginPwd';

  // 修改密码
  static const String changePassword = '/app/user/changeLoginPwd';

  //修改昵称
  static const String editNickName = '/app/user/editNickName';

  //发送验证码
  static const String getBindSmsCode = '/app/common/getBindSmsCode';

  //注销账号
  static const String deleteAccount = '/app/user/removeUser';

  /// 检查升级
  static const String checkUpgrade = '/app/coinbag/version';
}
