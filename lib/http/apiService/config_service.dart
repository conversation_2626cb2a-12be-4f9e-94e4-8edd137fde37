/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-08-04 13:45:11
 */

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:vcb/http/api_config.dart';
import 'package:vcb/http/dio/dio_provider.dart';
import 'package:vcb/http/response/config/config_response.dart';

part 'config_service.g.dart';

@RestApi()
abstract class ConfigService {
  factory ConfigService({Dio? dio, String? baseUrl}) {
    dio ??= DioClient().dio;
    return _ConfigService(dio, baseUrl: ApiConfig.configBaseUrl);
  }

  // App Config URLls
  @GET("")
  Future<ConfigResponse> getConfigBaseUrl();
}
