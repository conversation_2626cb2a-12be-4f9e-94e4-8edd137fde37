/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 13:04:38
 */

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:vcb/http/api_config.dart';
import 'package:vcb/http/dio/dio_provider.dart';

part 'api_service.g.dart';

@RestApi()
abstract class ApiService {
  factory ApiService({Dio? dio, String? baseUrl}) {
    dio ??= DioClient().dio;
    return _ApiService(dio, baseUrl: baseUrl ?? ApiConfig.baseUrl);
  }
}
