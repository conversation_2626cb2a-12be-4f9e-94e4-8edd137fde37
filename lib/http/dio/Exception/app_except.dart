/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-08-04 11:33:18
 */
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/utils/log_utils.dart';

/// 自定义异常
class AppException extends DioException {
  static String clientNetError = ID.clientNetError.tr;
  static String timeOutError = ID.timeOutError.tr;
  static String serverNetError = ID.serverNetError.tr;
  static String netConnectError = ID.netConnectError.tr;
  static String cancelConnectError = ID.cancelConnectError.tr;

  final String? msg;
  final int? code;
  DioExceptionType? errortype;
  AppException(this.code, this.msg, this.errortype,
      {required super.requestOptions});

  factory AppException.create(DioException error) {
    switch (error.type) {
      case DioExceptionType.cancel:
        return AppException(-1, cancelConnectError, DioExceptionType.cancel,
            requestOptions: RequestOptions());
      case DioExceptionType.connectionTimeout: //连接超时
      case DioExceptionType.sendTimeout: //请求超时
        return AppException(
            -1, timeOutError, DioExceptionType.connectionTimeout,
            requestOptions: RequestOptions());
      case DioExceptionType.receiveTimeout: //响应超时
        return AppException(-1, timeOutError, DioExceptionType.receiveTimeout,
            requestOptions: RequestOptions());
      case DioExceptionType.badResponse:
        try {
          int errCode = error.response!.statusCode!;

          switch (errCode) {
            case 400:
              return AppException(
                  errCode, serverNetError, DioExceptionType.badResponse,
                  requestOptions: RequestOptions());
            case 401: //没有权限
            case 403: //服务器拒绝执行
            case 404: //无法连接服务器
            case 405: //请求方法被禁止
            case 500: //服务器内部错误
            case 502: //无效的请求
            case 503: //服务器挂了
            case 505: //不支持HTTP协议请求
              Log.e("${"errCode=$errCode"} $serverNetError");
              return AppException(
                  errCode, serverNetError, DioExceptionType.badResponse,
                  requestOptions: RequestOptions());
            default:
              return AppException(errCode, error.response!.statusMessage!,
                  DioExceptionType.badResponse,
                  requestOptions: RequestOptions());
          }
        } on Exception catch (_) {
          return AppException(-1, netConnectError, DioExceptionType.badResponse,
              requestOptions: RequestOptions());
        }
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return AppException(-1, clientNetError, DioExceptionType.unknown,
              requestOptions: RequestOptions());
        } else {
          return AppException(-1, netConnectError, DioExceptionType.badResponse,
              requestOptions: RequestOptions());
        }
      case DioExceptionType.connectionError:
        if (error.error is SocketException) {
          final socketError = error.error as SocketException;
          final msg = socketError.osError?.message ?? socketError.message ?? '';
          // 常见本地断网
          if (msg.contains('Network is unreachable') ||
              msg.contains('No route to host') ||
              msg.contains('Failed host lookup') ||
              msg.contains('not connected') ||
              msg.contains('No address associated with hostname') ||
              msg.contains('nodename nor servname provided, or not known') ||
              msg.contains('Name or service not known')) {
            return AppException(
                -1, clientNetError, DioExceptionType.connectionError,
                requestOptions: RequestOptions());
          }
          // 服务器拒绝连接
          if (msg.contains('Connection refused') ||
              msg.contains('Connection timed out') ||
              msg.contains('Connection reset by peer')) {
            return AppException(
                -1, serverNetError, DioExceptionType.connectionError,
                requestOptions: RequestOptions());
          }
          // 其它未知 socket 错误
          return AppException(
              -1, netConnectError, DioExceptionType.connectionError,
              requestOptions: RequestOptions());
        } else {
          return AppException(
              -1, netConnectError, DioExceptionType.connectionError,
              requestOptions: RequestOptions());
        }
      default:
        return AppException(-1, netConnectError, DioExceptionType.unknown,
            requestOptions: RequestOptions());
    }
  }
}
