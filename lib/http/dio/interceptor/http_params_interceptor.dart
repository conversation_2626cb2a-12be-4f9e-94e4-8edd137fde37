/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:47:52
 * @LastEditTime: 2024-12-03 13:09:32
 */

import 'dart:convert';

import 'package:dio/dio.dart' as dio;
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:get/get.dart' hide Response;
import 'package:vcb/app/app_config.dart';
import 'package:vcb/constant/common_constant.dart';
import 'package:vcb/database/storage/secure_storage_service.dart';
import 'package:vcb/http/api_config.dart';
import 'package:vcb/http/dio/Exception/app_except.dart';
import 'package:vcb/modules/locale/locale_controller.dart';
import 'package:vcb/route/routes.dart';
import 'package:vcb/utils/log_utils.dart';
import 'package:vcb/utils/package_info_manager.dart';

///请求公共参数拦截器
class HttpParamsInterceptor extends dio.Interceptor {
  static const language = "language";
  static const appname = "appname";
  static const mobileType = "mobileType";
  static const mobileId = "mobileId";
  static const version = "version";

  @override
  void onRequest(
    dio.RequestOptions options,
    dio.RequestInterceptorHandler handler,
  ) {
    var headers = options.headers;
    headers[language] = LocaleController.getCountryCode;
    headers[mobileType] = GetPlatform.isAndroid ? "Android" : "IOS";
    headers[version] = PackageInfoManager().version;
    headers[appname] = PackageInfoManager().appName;
    if (AppConfig.instance.enableRequestJsonLog) {
      Log.r(
        '${"--------------------onRequest---------------"}\n${options.method}${' Url:'} ${options.uri}\n${'Query Parameters:'}${options.queryParameters}\n${'Body:'}${options.data != null ? const JsonEncoder.withIndent('  ').convert(options.data) : ""}\n\n',
      );
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(
    dio.Response response,
    dio.ResponseInterceptorHandler handler,
  ) {
    if (AppConfig.instance.enableResponseJsonLog) {
      Log.r(
        '${"-----------------------------Response Start-----------------------------"}\n${response.requestOptions.method}${' Url:'} ${response.requestOptions.uri}\n${'Query Parameters:'}${response.requestOptions.queryParameters}\n${'Body:'}${response.requestOptions.data != null ? const JsonEncoder.withIndent('  ').convert(response.requestOptions.data) : ""}\n${'response data:'}\n$response\n\n${"-----------------------------Response End-------------------------------"}\n\n\n',
      );
    }

    // 检查Token过期
    _checkTokenExpired(response);

    super.onResponse(response, handler);
  }

  @override
  Future<void> onError(
    dio.DioException err,
    dio.ErrorInterceptorHandler handler,
  ) async {
    AppException appException = AppException.create(err);
    if (AppConfig.instance.enableRequestLog) {
      Log.e('DioException===: +${err.toString()}');
    }

    if (AppConfig.instance.enableBuglyHttpLog) {
      try {
        final buglyError = StringBuffer()
          ..writeln('API Error: ${err.response?.statusCode}')
          ..writeln('url: ${err.requestOptions.uri}')
          ..writeln('method: ${err.requestOptions.method}')
          ..writeln('dioType: ${err.type}') // 记录异常类型
          ..writeln('error: ${appException.msg ?? err.message}')
          ..writeln('requestHeaders: ${jsonEncode(err.requestOptions.headers)}')
          ..writeln(
            'requestQuery: ${jsonEncode(err.requestOptions.queryParameters)}',
          )
          ..writeln(
            'requestBody: ${err.requestOptions.data != null ? jsonEncode(err.requestOptions.data) : ''}',
          )
          ..writeln(
            'responseData: ${err.response?.data != null ? jsonEncode(err.response?.data) : ''}',
          );

        FlutterBugly.uploadException(
          message: buglyError.toString(),
          detail: 'httpError',
          type: 'API Error',
        );

        Log.e('FlutterBugly DioException===: +${buglyError.toString()}');
      } catch (_) {}
    }

    Log.logPrint(err.requestOptions.headers);
    Log.logPrint(appException.type);
    Map<String, dynamic> data = err.requestOptions.headers;
    bool? isIntercep = data[APIConstant.ignoreIntercep];
    if (isIntercep == true &&
        (appException.type == dio.DioExceptionType.badResponse ||
            appException.type == dio.DioExceptionType.unknown)) {
      return handler.resolve(
        dio.Response<Map<String, dynamic>>(
          requestOptions: err.requestOptions,
          statusCode: 200,
          data: {},
        ),
      );
    }

    return handler.next(appException);
  }

  /// 检查Token过期
  void _checkTokenExpired(dio.Response response) {
    try {
      // 检查响应数据是否包含code字段
      if (response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        final code = data['code'];

        // 检查是否是Token过期错误码 10001
        if (code == APIConstant.codeJwtExpired) {
          Log.logPrint("🔑 检测到Token过期，错误码: $code");
          _handleTokenExpired();
        }
      }
    } catch (e) {
      Log.e("检查Token过期时发生错误: $e");
    }
  }

  /// 处理Token过期
  void _handleTokenExpired() async {
    try {
      // 1. 清除本地存储的Token
      await SecureStorageService.clearAuthData();

      // 2. 检查当前是否已经在登录页面，避免重复跳转
      final currentRoute = Get.currentRoute;
      if (currentRoute == AppRoutes.loginPage ||
          currentRoute == AppRoutes.authPage) {
        Log.logPrint("⚠️ 当前已在登录页面，跳过跳转");
        return;
      }

      // 3. 延迟执行，确保当前请求处理完成
      await Future.delayed(Duration(milliseconds: 100));

      // 4. 跳转到授权页面
      Log.logPrint("🔄 跳转到授权页面");
      Get.offAllNamed(AppRoutes.authPage);
    } catch (e) {
      Log.e("处理Token过期时发生错误: $e");
    }
  }
}
