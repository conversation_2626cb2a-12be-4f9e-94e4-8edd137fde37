/*
 * @author: Chend
 * @description: 
 * @LastEditTime: 2025-08-04 11:34:44
 */

class Extra {
  Extra();

  Map<String, dynamic> toJson() => {};

  Map<String, dynamic> toBalanceJson() => {
        'all_balance': true,
      };

  Map<String, dynamic> toContractBalanceJson(String contract) => {
        'contract': contract,
      };

  Map<String, dynamic> toSolContractBalanceJson({
    required String contract,
    required String derivedAddresses,
  }) {
    return {
      'contract': contract,
      'tokenDerivedAddresses': derivedAddresses,
    };
  }

  Map<String, dynamic> toTokenActivityJson([String? tokenDerivedAddresses]) => {
        'amount_abs': true,
        'tokenDerivedAddresses': tokenDerivedAddresses ?? '',
      };
  Map<String, dynamic> toBitcoinSeriesUnconfirmed() => {
        'zero_unconfirmed': true,
      };

  Map<String, dynamic> toEosOrTronTokenContractBalanceJson(
          String contract, String symbol) =>
      {
        'contract': contract,
        'symbol': symbol,
      };
}
