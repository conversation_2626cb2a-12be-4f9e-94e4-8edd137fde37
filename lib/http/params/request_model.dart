import 'package:json_annotation/json_annotation.dart';

part 'request_model.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class RequestModel<T> {
  final int? code;
  final String? message;
  final T? data;

  RequestModel({this.code, this.message, this.data});

  factory RequestModel.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$RequestModelFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$RequestModelToJson(this, toJsonT);
}
