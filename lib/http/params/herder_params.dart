/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-03-19 19:08:14
 */
/// 通用Herder请求参数封装
class HerderParams {
  HerderParams();

  final Map<String, dynamic> _params = {};

  HerderParams put(String key, dynamic value) {
    if (value != null) {
      _params[key] = value;
    }
    return this;
  }

  void remove(String key) {
    _params.remove(key);
  }

  Map<String, dynamic> getRequestBody() {
    return _params;
  }
}
