/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-08-12 17:17:52
 */
import 'domain.dart';

class ConfigResponse {
  Domain? domain;

  ConfigResponse({this.domain});

  factory ConfigResponse.fromJson(Map<String, dynamic> json) => ConfigResponse(
        domain: json['domain'] == null
            ? null
            : Domain.fromJson(json['domain'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'domain': domain?.toJson(),
      };
}
