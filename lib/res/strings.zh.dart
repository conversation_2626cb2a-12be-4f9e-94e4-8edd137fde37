import 'strings.dart';

// zh
const Map<String, String> localizedValueZH = {
  ID.appName: 'VCB',
  ID.exitApplication: '再按一次退出应用',
  ID.cameraAccessNotEnabled: '相机权限未开启',
  ID.stringTips: '提示',
  ID.gotoSettingTitle: '去设置',
  ID.storageAccessNotEnabled: '未开启存储权限',
  ID.stringBackButton: '返回按钮',
  ID.stringDeny: '拒绝',
  ID.stringConfirm: '确认',
  ID.stringCancel: '取消',
  ID.releaseText: '释放立即刷新',
  ID.refreshingText: '正在刷新...',
  ID.completeText: '刷新完成',
  ID.idleText: '下拉可以刷新',
  ID.loadingText: '正在加载中...',
  ID.pullUpToLoad: '上拉加载更多',
  ID.canLoadingText: '松手加载更多',
  ID.noDataText: '没有更多数据了',
  ID.emptyData: '暂无数据',
  ID.dataError: '加载失败',
  ID.reload: '重新加载',
  ID.stringCopySuccess: '复制成功',
  ID.loading: '加载中...',
  ID.clientNetError: '网络异常，请检网络连接状态',
  ID.timeOutError: '请求超时!请稍后再试',
  ID.serverNetError: '服务器连接出现问题\n请稍后重试或联系客服处理',
  ID.netConnectError: '网络未连接，请检查后重试',
  ID.cancelConnectError: '请求取消',
  ID.stringHome: '首页',
  ID.stringProfile: '我的',
  ID.stringEarn: '理财',
  ID.stringLoan: '借贷',
  ID.stringAssets: '资产',
  ID.stringPhone: '电话',
  ID.stringEmail: '邮箱',
  ID.stringLogin: '登录',
  ID.stringPhoneHintText: '输入手机号',
  ID.stringEmailHintText: '输入邮箱',
  ID.stringPassword: '密码',
  ID.stringPasswordHintText: '输入密码',
  ID.loginForgetPassword: '忘记密码？',
  ID.loginRegisterNew: '注册新账号',
  ID.loginOkPhone: '请输入正确的手机号',
  ID.loginOkEmail: '请输入正确的邮箱',
  ID.stringLoginSuccess: '登录成功',
  ID.stringRegister: '注册',
  ID.stringAgreement1: '我已阅读并同意',
  ID.stringAgreement2: '《用户协议》',
  ID.stringHasAccount: '已有账号，',
  ID.copy: '复制',
  ID.scanResult: '扫描结果',
  ID.scan: '扫一扫',
  ID.share: '分享',
  ID.penInBrowser: '浏览器打开',
  ID.copyLink: '复制链接',
  ID.refresh: '刷新',
  ID.stingCannotAccessThisWebsite: '无法访问此网站',
  ID.stingCannotAccessThisWebsiteVPN: '访问网站出错，请尝试重新加载或使用VPN',
  ID.stringRedirecting: '正在跳转三方网站',
  ID.stringUserName: '用户名',
  ID.stringTotalAssets: '总资产',
  ID.stringAvailableBalance: '可用余额',
  ID.stringFrozenBalance: '冻结余额',
  ID.stringOutstandingDebt: '未还欠款',
  ID.stringLeverageRatio: '杠杆率',
  ID.stringDeposit: '充币',
  ID.stringWithdrawal: '提币',
  ID.stringBill: '账单',
  ID.stringCashAssets: '现金资产',
  ID.stringInvestmentAssets: '理财资产',
  ID.stringCurrent: '活期',
  ID.stringFixed: '定期',
  ID.stringLendingAssets: '借贷资产',
  ID.stringBorrowing: '借入',
  ID.stringLending: '借出',
  ID.stringMinimumInterestRate: '最低利率',
};
