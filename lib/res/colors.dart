import 'package:flutter/material.dart';

/// @description :颜色新增需考虑暗黑模式,新增颜色必须这里添加
abstract class AppThemeColors {
  Color get black; //黑色 纯黑 禁止直接使用 与主题黑不是一个颜色
  Color get white; //白色 纯白 禁止直接使用，除非夜间模式也是白
  Color get bgColor; //背景白色
  Color get primary; //主题黑色（例如Button主色，文本）
  Color get primaryBlue; //主题蓝色 c_m
  Color get colorF9F9F9; //说明文案背景色 c_b_m_bg
  Color get colorF3F3F5; //输入框背景色(输入框) i_bg
  Color get color0A1D2023; //条目鼠标经过 e_h_bg
  Color get textPrimary; //文字主题一级颜色 c_t01
  Color get textSecondary; //文字主题二级颜色 c_t02
  Color get textTertiary; //文字主题三级级颜色 c_t03
  Color get color02B58A; //绿 c_c
  Color get colorF44D4D; //红 c_e
  Color get colorF4454545; //Toast颜色c_t_bg
  Color get colorD3D3D3; //通用元素描边线c_s_b01
  Color get colorECECEC; //分割线 c_s_b02
  Color get buttonFocusColor; //按钮选中色focusColor FF121212
  Color get colorFFF2DA; // 个人中心头部渐变色
  Color get colorFD8114; // 带背景颜色的 橙色颜色
  Color get colorD7D7D7;
  Color get color1AFF6A16; //主题10%色
  Color get color6C60FF; //主题10%色

  // ...可以定义更多颜色属性
}

// 扩展 ThemeData 新增颜色添加后还需要在这里设置引用
extension CustomThemeColors on ThemeData {
  Color get white => getThemeColors(this).white;
  Color get black => getThemeColors(this).black;
  Color get bgColor => getThemeColors(this).bgColor;
  Color get primary => getThemeColors(this).primary;
  Color get primaryBlue => getThemeColors(this).primaryBlue;
  Color get colorF9F9F9 => getThemeColors(this).colorF9F9F9;
  Color get colorF3F3F5 => getThemeColors(this).colorF3F3F5;
  Color get color0A1D2023 => getThemeColors(this).color0A1D2023;
  Color get textPrimary => getThemeColors(this).textPrimary;
  Color get textSecondary => getThemeColors(this).textSecondary;
  Color get textTertiary => getThemeColors(this).textTertiary;
  Color get color02B58A => getThemeColors(this).color02B58A;
  Color get colorF44D4D => getThemeColors(this).colorF44D4D;
  Color get colorF5454545 => getThemeColors(this).colorF4454545;
  Color get colorD3D3D3 => getThemeColors(this).colorD3D3D3;
  Color get colorECECEC => getThemeColors(this).colorECECEC;
  Color get buttonFocusColor => getThemeColors(this).buttonFocusColor;
  Color get colorFFF2DA => getThemeColors(this).colorFFF2DA;
  Color get colorFD8114 => getThemeColors(this).colorFD8114;
  Color get colorD7D7D7 => getThemeColors(this).colorD7D7D7;
  Color get color1AFF6A16 => getThemeColors(this).color1AFF6A16;
  Color get color6C60FF => getThemeColors(this).color6C60FF;
}

AppThemeColors getThemeColors(ThemeData themeData) {
  return themeData.brightness == Brightness.dark
      ? DarkAppThemeColors()
      : LightAppThemeColors();
}

class LightAppThemeColors implements AppThemeColors {
  @override
  Color get black => const Color(0xFF000000);

  @override
  Color get color02B58A => const Color(0xFF02B58A);

  @override
  Color get color0A1D2023 => const Color(0x0A1D2023);

  @override
  Color get colorD3D3D3 => const Color(0xFFD3D3D3);

  @override
  Color get colorECECEC => const Color(0xFFECECEC);

  @override
  Color get colorF3F3F5 => const Color(0xFFF3F3F5);

  @override
  Color get colorF44D4D => const Color(0xFFF44D4D);

  @override
  Color get colorF4454545 => const Color(0xF4454545);

  @override
  Color get colorF9F9F9 => const Color(0xFFF9F9F9);

  @override
  Color get primaryBlue => const Color(0xFF0058E9);

  @override
  Color get primary => const Color(0xFF121212);

  @override
  Color get bgColor => const Color(0xFFFFFFFF);

  @override
  Color get textPrimary => const Color(0xFF121212);

  @override
  Color get textSecondary => const Color(0xFF666666);

  @override
  Color get textTertiary => const Color(0xFF999999);

  @override
  Color get white => const Color(0xFFFFFFFF);

  @override
  Color get buttonFocusColor => const Color(0xF2121212);

  @override
  Color get colorFFF2DA => const Color(0xFFFFF2DA);

  @override
  Color get colorFD8114 => const Color(0xFFFD8114);

  @override
  Color get colorD7D7D7 => const Color(0xFFD7D7D7);

  @override
  Color get color1AFF6A16 => const Color(0x1AFF6A16);

  @override
  Color get color6C60FF => const Color(0xFF6c60ff);
}

///？TODO 暗黑主题后续会加，目前和亮色保持一致
class DarkAppThemeColors implements AppThemeColors {
  @override
  Color get black => const Color(0xFF000000);

  @override
  Color get color02B58A => const Color(0xFF02B58A);

  @override
  Color get color0A1D2023 => const Color(0x0A1D2023);

  @override
  Color get colorD3D3D3 => const Color(0xFFD3D3D3);

  @override
  Color get colorECECEC => const Color(0xFFECECEC);

  @override
  Color get colorF3F3F5 => const Color(0xFFF3F3F5);

  @override
  Color get colorF44D4D => const Color(0xFFF44D4D);

  @override
  Color get colorF4454545 => const Color(0xF4454545);

  @override
  Color get colorF9F9F9 => const Color(0xFFF9F9F9);

  @override
  Color get primaryBlue => const Color(0xFF0058E9);

  @override
  Color get primary => const Color(0xFF121212);

  @override
  Color get bgColor => const Color(0xFFFFFFFF);

  @override
  Color get textPrimary => const Color(0xFF121212);

  @override
  Color get textSecondary => const Color(0xFF666666);

  @override
  Color get textTertiary => const Color(0xFF999999);

  @override
  Color get white => const Color(0xFFFFFFFF);

  @override
  Color get buttonFocusColor => const Color(0xF2121212);

  @override
  Color get colorFFF2DA => const Color(0xFFFFF2DA);

  @override
  Color get colorFD8114 => const Color(0xFFFD8114);

  @override
  Color get colorD7D7D7 => const Color(0xFFD7D7D7);

  @override
  Color get color1AFF6A16 => const Color(0x1AFF6A16);

  @override
  Color get color6C60FF => const Color(0xFF6c60ff);
}
