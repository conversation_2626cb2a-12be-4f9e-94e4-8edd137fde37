import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/colors.dart';
import 'package:vcb/res/fonts.dart';
import 'package:vcb/widgets/tab_bar/shape_tab_decoration.dart';

// primary：应用主要部分的背景颜色。
// primaryVariant：主要颜色的深色版本。
// secondary：用于强调界面部分的颜色。
// secondaryVariant：强调颜色的深色版本。
// surface：表面的颜色，例如 Card、 Dialog 等。
// background：应用的背景颜色。
// error：用于错误提示的颜色。
// onPrimary：在主要颜色上面用于文本和图标的颜色。
// onSecondary：在强调颜色上面用于文本和图标的颜色。
// onSurface：在表面颜色上面用于文本和图标的颜色。
// onBackground：在背景颜色上面用于文本和图标的颜色。
// onError：在错误颜色上面用于文本和图标的颜色。
ThemeData lightTheme = ThemeData.light().copyWith(
  visualDensity: VisualDensity.adaptivePlatformDensity,
  colorScheme: ColorScheme(
    primary: LightAppThemeColors().primary,
    secondary: LightAppThemeColors().primary,
    surface: LightAppThemeColors().colorECECEC,
    error: LightAppThemeColors().colorF44D4D,
    onPrimary: LightAppThemeColors().primary,
    onSecondary: LightAppThemeColors().textTertiary,
    onSurface: LightAppThemeColors().primary,
    onError: LightAppThemeColors().colorF44D4D,
    brightness: Brightness.light,
  ),
  appBarTheme: AppBarTheme(
    elevation: 0,
    centerTitle: true,
    surfaceTintColor: LightAppThemeColors().bgColor,
    systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarColor: LightAppThemeColors().primary,
        systemNavigationBarIconBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark),
    backgroundColor: LightAppThemeColors().bgColor,
    iconTheme: IconThemeData(color: LightAppThemeColors().primary),
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
          textStyle: WidgetStateProperty.all(TextStyle(
              fontSize: 16,
              color: LightAppThemeColors().primary,
              fontFamily: Get.setFontFamily(),
              fontWeight: FontWeightX.medium)),
          backgroundColor:
              WidgetStateProperty.all<Color>(LightAppThemeColors().bgColor),
          overlayColor: WidgetStateProperty.resolveWith<Color?>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return LightAppThemeColors().colorF4454545;
              }
              return LightAppThemeColors().bgColor;
            },
          ))),
  scaffoldBackgroundColor: LightAppThemeColors().white,
  radioTheme: RadioThemeData(
    fillColor: WidgetStateProperty.all(LightAppThemeColors().primary),
  ),
  checkboxTheme: CheckboxThemeData(
    fillColor: WidgetStateProperty.all(LightAppThemeColors().primary),
  ),
  textTheme: TextTheme(
    headlineLarge: TextStyle(
        fontSize: 16,
        fontFamily: Get.setFontFamily(),
        fontWeight: FontWeightX.regular,
        color: LightAppThemeColors().textPrimary),
    headlineMedium: TextStyle(
        fontSize: 16,
        fontWeight: FontWeightX.medium,
        fontFamily: Get.setFontFamily(),
        color: LightAppThemeColors().textPrimary),
    bodyLarge: TextStyle(
        fontSize: 16,
        fontFamily: Get.setFontFamily(),
        color: LightAppThemeColors().textPrimary),
    bodyMedium: TextStyle(
        fontSize: 14,
        fontFamily: Get.setFontFamily(),
        color: LightAppThemeColors().textPrimary),
    bodySmall: TextStyle(
        fontSize: 12,
        fontFamily: Get.setFontFamily(),
        color: LightAppThemeColors().textPrimary),
    displaySmall: TextStyle(
        fontSize: 12,
        fontFamily: Get.setFontFamily(),
        color: LightAppThemeColors().textPrimary),
    displayMedium: TextStyle(
        fontSize: 14,
        fontFamily: Get.setFontFamily(),
        color: LightAppThemeColors().textPrimary),
    displayLarge: TextStyle(
        fontSize: 16,
        fontFamily: Get.setFontFamily(),
        color: LightAppThemeColors().textPrimary),
  ),
  tabBarTheme: TabBarThemeData(
    indicatorSize: TabBarIndicatorSize.label,
    indicatorColor: LightAppThemeColors().colorECECEC,
    overlayColor: WidgetStateProperty.resolveWith((states) {
      return Colors.transparent;
    }),
    labelStyle: TextStyle(
        color: LightAppThemeColors().textPrimary,
        fontSize: 14,
        fontWeight: FontWeightX.medium,
        fontFamily: Get.setFontFamily()),
    unselectedLabelStyle: TextStyle(
        color: LightAppThemeColors().textTertiary,
        fontSize: 14,
        fontWeight: FontWeightX.medium,
        fontFamily: Get.setFontFamily()),
    indicator: ShapeTabIndicator(
        indicatorBottom: 0,
        indicatorWidth: 16,
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        borderSide: BorderSide(
          width: 2,
          color: LightAppThemeColors().primary,
        )),
  ),
);

///？TODO 暗黑主题后续会加，目前和亮色保持一致
ThemeData darkTheme = ThemeData.dark().copyWith(
  visualDensity: VisualDensity.adaptivePlatformDensity,
  colorScheme: ColorScheme(
    primary: DarkAppThemeColors().primary,
    secondary: DarkAppThemeColors().primary,
    surface: DarkAppThemeColors().colorECECEC,
    error: DarkAppThemeColors().colorF44D4D,
    onPrimary: DarkAppThemeColors().primary,
    onSecondary: DarkAppThemeColors().textTertiary,
    onSurface: DarkAppThemeColors().primary,
    onError: DarkAppThemeColors().colorF44D4D,
    brightness: Brightness.dark,
  ),
  appBarTheme: AppBarTheme(
    elevation: 0,
    centerTitle: true,
    surfaceTintColor: DarkAppThemeColors().bgColor,
    systemOverlayStyle: SystemUiOverlayStyle(
        statusBarBrightness: Brightness.light,
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: DarkAppThemeColors().primary,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.dark),
    backgroundColor: DarkAppThemeColors().bgColor,
    iconTheme: IconThemeData(color: DarkAppThemeColors().primary),
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
      style: ButtonStyle(
          textStyle: WidgetStateProperty.all(TextStyle(
              fontSize: 16,
              color: DarkAppThemeColors().primary,
              fontFamily: Get.setFontFamily(),
              fontWeight: FontWeightX.medium)),
          backgroundColor:
              WidgetStateProperty.all<Color>(DarkAppThemeColors().bgColor),
          overlayColor: WidgetStateProperty.resolveWith<Color?>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return DarkAppThemeColors().colorF4454545;
              }
              return DarkAppThemeColors().bgColor;
            },
          ))),
  scaffoldBackgroundColor: DarkAppThemeColors().white,
  radioTheme: RadioThemeData(
    fillColor: WidgetStateProperty.all(DarkAppThemeColors().primary),
  ),
  checkboxTheme: CheckboxThemeData(
    fillColor: WidgetStateProperty.all(DarkAppThemeColors().primary),
  ),
  textTheme: TextTheme(
    headlineLarge: TextStyle(
        fontSize: 16,
        fontFamily: Get.setFontFamily(),
        fontWeight: FontWeightX.regular,
        color: DarkAppThemeColors().textPrimary),
    headlineMedium: TextStyle(
        fontSize: 16,
        fontWeight: FontWeightX.medium,
        fontFamily: Get.setFontFamily(),
        color: DarkAppThemeColors().textPrimary),
    bodyLarge: TextStyle(
        fontSize: 16,
        fontFamily: Get.setFontFamily(),
        color: DarkAppThemeColors().textPrimary),
    bodyMedium: TextStyle(
        fontSize: 14,
        fontFamily: Get.setFontFamily(),
        color: DarkAppThemeColors().textPrimary),
    bodySmall: TextStyle(
        fontSize: 12,
        fontFamily: Get.setFontFamily(),
        color: DarkAppThemeColors().textPrimary),
    displaySmall: TextStyle(
        fontSize: 12,
        fontFamily: Get.setFontFamily(),
        color: DarkAppThemeColors().textPrimary),
    displayMedium: TextStyle(
        fontSize: 14,
        fontFamily: Get.setFontFamily(),
        color: DarkAppThemeColors().textPrimary),
    displayLarge: TextStyle(
        fontSize: 16,
        fontFamily: Get.setFontFamily(),
        color: DarkAppThemeColors().textPrimary),
  ),
  tabBarTheme: TabBarThemeData(
    indicatorSize: TabBarIndicatorSize.label,
    indicatorColor: DarkAppThemeColors().colorECECEC,
    overlayColor: WidgetStateProperty.resolveWith((states) {
      return Colors.transparent;
    }),
    labelStyle: TextStyle(
        color: DarkAppThemeColors().textPrimary,
        fontSize: 14,
        fontWeight: FontWeightX.medium,
        fontFamily: Get.setFontFamily()),
    unselectedLabelStyle: TextStyle(
        color: DarkAppThemeColors().textTertiary,
        fontSize: 14,
        fontWeight: FontWeightX.medium,
        fontFamily: Get.setFontFamily()),
    indicator: ShapeTabIndicator(
        indicatorBottom: 0,
        indicatorWidth: 16,
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        borderSide: BorderSide(
          width: 2,
          color: DarkAppThemeColors().primary,
        )),
  ),
);
