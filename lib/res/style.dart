// ignore_for_file: constant_identifier_names

/*
 * @author: wds
 * @description: 字体样式规范
 * @LastEditTime: 2025-08-07 13:52:04
 */

import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';

/// =================== 字体样式规范 ===================

/// 普通正文 14号 Medium
TextStyle get textNormal14Medium => TextStyle(
      color: Get.theme.textPrimary,
      fontSize: Get.setFontSize(14),
      fontWeight: FontWeightX.medium,
      fontFamily: Get.setFontFamily(),
    );

/// 一级标题 14号 Medium
TextStyle get textPrimary14Medium => TextStyle(
      color: Get.theme.textPrimary,
      fontSize: Get.setFontSize(14),
      fontWeight: FontWeightX.medium,
      fontFamily: Get.setFontFamily(),
    );

/// 一级标题 16号 Medium
TextStyle get textPrimary16Medium => TextStyle(
      color: Get.theme.textPrimary,
      fontSize: Get.setFontSize(16),
      fontWeight: FontWeightX.medium,
      fontFamily: Get.setFontFamily(),
    );

/// 蓝色主题 14号 Medium
TextStyle get textBluePrimary14Medium => TextStyle(
      color: Get.theme.textPrimary,
      fontSize: Get.setFontSize(14),
      fontWeight: FontWeightX.medium,
      fontFamily: Get.setFontFamily(),
    );

/// 二级正文 14号 Regular
TextStyle get textSecondary14Regular => TextStyle(
      color: Get.theme.textSecondary,
      fontSize: Get.setFontSize(14),
      fontWeight: FontWeightX.regular,
      fontFamily: Get.setFontFamily(),
    );

/// 二级正文 16号 Regular
TextStyle get textSecondary16Regular => TextStyle(
      color: Get.theme.textSecondary,
      fontSize: Get.setFontSize(16),
      fontWeight: FontWeightX.regular,
      fontFamily: Get.setFontFamily(),
    );

/// AppBar 标题 18号 Medium
TextStyle get textAppBarTitle18Medium => TextStyle(
      color: Get.theme.textPrimary,
      fontSize: Get.setFontSize(18),
      fontWeight: FontWeightX.medium,
      fontFamily: Get.setFontFamily(),
    );
