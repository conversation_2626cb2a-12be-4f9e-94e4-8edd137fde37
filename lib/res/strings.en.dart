import 'strings.dart';

// en
const Map<String, String> localizedValueEN = {
  ID.appName: 'VCB',
  ID.exitApplication: 'Press again to exit the app',
  ID.cameraAccessNotEnabled: 'Camera Permission Not Granted',
  ID.stringTips: 'Tips',
  ID.gotoSettingTitle: 'Go to settings',
  ID.storageAccessNotEnabled: 'Storage Permission Not Granted',
  ID.stringBackButton: 'Back Button',
  ID.stringDeny: 'Deny',
  ID.stringConfirm: 'Confirm',
  ID.stringCancel: 'Cancel',
  ID.releaseText: 'Refresh immediately',
  ID.refreshingText: 'Refreshing...',
  ID.completeText: 'Refresh complete',
  ID.idleText: 'Pull down to refresh',
  ID.loadingText: 'Loading...',
  ID.pullUpToLoad: 'Pull up to load more',
  ID.canLoadingText: 'Release to load more',
  ID.noDataText: 'No more data',
  ID.emptyData: 'No data available',
  ID.dataError: 'Loading failed',
  ID.reload: 'Reload',
  ID.stringCopySuccess: 'Copy Successful',
  ID.loading: 'Loading...',
  ID.clientNetError: 'Network error, please check your network connection',
  ID.timeOutError: 'Request timed out! Please try again later',
  ID.serverNetError:
      'There is a problem with the server connection\nPlease try again later or contact customer support',
  ID.netConnectError: 'Network not connected, please check and try again',
  ID.cancelConnectError: 'Request canceled',
  ID.stringHome: 'Home',
  ID.stringProfile: 'Profile',
  ID.stringEarn: 'Earn',
  ID.stringLoan: 'Loan',
  ID.stringAssets: 'Assets',
  ID.stringPhone: 'Phone',
  ID.stringEmail: 'Email',
  ID.stringLogin: 'Login',
  ID.stringPhoneHintText: 'Enter Phone Number',
  ID.stringEmailHintText: 'Enter Email',
  ID.stringPassword: 'Password',
  ID.stringPasswordHintText: 'Enter Password',
  ID.loginForgetPassword: 'Forgot Password?',
  ID.loginRegisterNew: 'Register New Account',
  ID.loginOkPhone: 'Please enter a valid phone number',
  ID.loginOkEmail: 'Please enter a valid email',
  ID.stringLoginSuccess: 'Login Successful',
  ID.stringRegister: 'Register',
  ID.stringAgreement1: 'I have read and agree to the',
  ID.stringAgreement2: 'User Agreement',
  ID.stringHasAccount: 'Already have an account,',
  ID.copy: 'Copy',
  ID.scanResult: 'Scan Result',
  ID.scan: 'Scan',
  ID.share: 'Share',
  ID.penInBrowser: 'Open in browser',
  ID.copyLink: 'Copy link',
  ID.refresh: 'Refresh',
  ID.stingCannotAccessThisWebsite: 'Cannot access this website',
  ID.stingCannotAccessThisWebsiteVPN:
      'There was an error accessing the website, please try reloading or using a VPN',
  ID.stringRedirecting: 'Redirecting to a third-party website',
  ID.stringUserName: 'Username',
  ID.stringTotalAssets: 'Total Assets',
  ID.stringAvailableBalance: 'Available Balance',
  ID.stringFrozenBalance: 'Frozen Balance',
  ID.stringOutstandingDebt: 'Outstanding Debt',
  ID.stringLeverageRatio: 'Leverage Ratio',
  ID.stringDeposit: 'Deposit',
  ID.stringWithdrawal: 'Withdrawal',
  ID.stringBill: 'Bill',
  ID.stringCashAssets: 'Cash Assets',
  ID.stringInvestmentAssets: 'Investment Assets',
  ID.stringCurrent: 'Current',
  ID.stringFixed: 'Fixed',
  ID.stringLendingAssets: 'Lending Assets',
  ID.stringBorrowing: 'Borrowing',
  ID.stringLending: 'Lending',
  ID.stringMinimumInterestRate: 'Minimum Interest Rate',
};
