/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-04 14:35:30
 * @LastEditTime: 2024-03-12 16:55:06
 */
import 'package:flutter/material.dart';
import 'package:vcb/widgets/tab_bar/base_tab_bar.dart';

class TabBarWidget extends StatelessWidget {
  const TabBarWidget({
    super.key,
    required this.tabs,
    this.controller,
    this.labelStyle,
    this.unselectedLabelStyle,
    this.isScrollable = false,
    this.labelPadding,
    this.onTab,
  });

  final bool isScrollable;
  final TabController? controller;
  final TextStyle? labelStyle;
  final TextStyle? unselectedLabelStyle;
  final List<Widget>? tabs;
  final EdgeInsetsGeometry? labelPadding;
  final ValueChanged<int>? onTab;

  @override
  Widget build(BuildContext context) {
    return baseTabBar(
      isScrollable: isScrollable,
      controller: controller,
      labelStyle: labelStyle,
      unselectedLabelStyle: unselectedLabelStyle,
      tabs: tabs,
      labelPadding: labelPadding,
      onTab: onTab,
    );
  }
}
