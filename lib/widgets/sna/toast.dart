/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-13 12:58:25
 * @LastEditTime: 2025-08-04 17:48:47
 */
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/image/image_widget.dart';

enum ToastMode { normal, success, failed, waring }

void showToastMessage(
  String? msg, {
  bool isShortToast = true,
  ToastMode toastMode = ToastMode.normal,
  int maxLines = 3,
}) {
  showToastWidget(
      Container(
        padding: EdgeInsets.all(Get.setWidth(14)),
        margin: EdgeInsets.symmetric(horizontal: Get.setWidth(40)),
        decoration: ShapeDecoration(
          color: Get.theme.colorF5454545,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(Get.setWidth(12)),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Text.rich(
                  key: Key('vcb_toast'),
                  TextSpan(children: <InlineSpan>[
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Visibility(
                        visible: toastMode != ToastMode.normal,
                        child: Padding(
                          padding: EdgeInsets.only(right: Get.setWidth(8)),
                          child: ImageWidget(
                              assetUrl: getPath(toastMode),
                              width: Get.setImageSize(20),
                              height: Get.setImageSize(20)),
                        ),
                      ),
                    ),
                    TextSpan(
                      text: msg ?? "",
                      style: TextStyle(
                          color: Get.theme.white,
                          fontSize: Get.setFontSize(16),
                          fontFamily: Get.setFontFamily(),
                          fontWeight: FontWeightX.regular,
                          overflow: TextOverflow.ellipsis),
                    ),
                  ]),
                  maxLines: maxLines,
                  overflow: TextOverflow.ellipsis),
            ),
          ],
        ),
      ),
      duration: Duration(milliseconds: isShortToast ? 2000 : 4000),
      position: ToastPosition.center);
}

String getPath(ToastMode toastMode) {
  String path;
  switch (toastMode) {
    case ToastMode.success:
      path = "icon_success_toast";
      break;
    case ToastMode.failed:
      path = "icon_failed_toast";
      break;
    case ToastMode.waring:
      path = "icon_warning_toast";
      break;
    case ToastMode.normal:
      path = "";

      break;
  }

  return path;
}
