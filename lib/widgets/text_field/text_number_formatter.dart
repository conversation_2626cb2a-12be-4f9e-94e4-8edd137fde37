/*
 * @author: Chend
 * @description: 
 * @LastEditTime: 2024-04-12 16:02:39
 */
import 'package:flutter/services.dart';

class TextNumberInputFormatter extends TextInputFormatter {
  int? scale;
  TextInputType inputType;
  TextNumberInputFormatter({this.scale, required this.inputType});

  String get _pointer {
    return '.';
  }

  bool get _number => inputType.decimal == false;

  static RegExp decimalRegExp = RegExp(r'^[0-9.]+$');
  static RegExp numberRegExp = RegExp(r'^[0-9]+$');

  static RegExp regExp(bool decimal) {
    return decimal ? decimalRegExp : numberRegExp;
  }

  static List<TextInputFormatter> numberInputFormatter(
      {int? scale, required TextInputType inputType}) {
    return [
      TextNumberInputFormatter(scale: scale, inputType: inputType),
      FilteringTextInputFormatter.allow(regExp(inputType.decimal == true))
    ];
  }

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // 删除的时候不校验
    if (newValue.text.length < oldValue.text.length) {
      return newValue;
    }

    /// 输入是否数字或者.之外的字符
    bool result = regExp(inputType.decimal == true).hasMatch(newValue.text);
    if (!result) {
      return oldValue;
    }

    // 如果键盘设置的是 Number 则第一位不能输入0
    if (_number) {
      if (newValue.text.startsWith('0')) {
        return oldValue;
      }
    }

    // 第一位输入小数点，默认加上0.
    if (newValue.text.startsWith(_pointer)) {
      return const TextEditingValue(text: '0.');
    }

    // 开头不能以多个0开头
    if (newValue.text.startsWith('00')) {
      return oldValue;
    }

    // 0开头后面只能跟.
    if (newValue.text.startsWith('0') && newValue.text.length > 1) {
      if (newValue.text.split("0")[1].startsWith(numberRegExp)) {
        return oldValue;
      }
    }

    if (newValue.text.contains(_pointer)) {
      // 第一个位和最后一位的位置不相等则证明存在至少两个.
      if (newValue.text.indexOf(_pointer) !=
          newValue.text.lastIndexOf(_pointer)) {
        return oldValue;
      }

      // 保留小数位
      if (scale != null) {
        String input = newValue.text;
        int index = input.indexOf(_pointer);

        //小数点后位数
        int decimalLength = input.substring(index, input.length - 1).length;

        //小数位大于精度
        if (decimalLength > scale!) {
          return oldValue;
        }
      }
    }

    return newValue;
  }
}
