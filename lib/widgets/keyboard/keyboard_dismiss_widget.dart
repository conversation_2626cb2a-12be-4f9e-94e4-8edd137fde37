/*
 * @description: Do not edit
 * @Author: wangdog<PERSON>henng
 * @Date: 2024-01-09 18:16:49
 * @LastEditTime: 2024-03-28 13:30:47
 */
import 'package:flutter/cupertino.dart';
import 'package:vcb/utils/keyboard_util.dart';

///全局控制点击空白处关闭键盘输入,如ios关闭软键盘
class KeyboardDismissWidget extends StatelessWidget {
  final Widget? child;

  const KeyboardDismissWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => KeyboardUtils.hideKeyboard(context),
        child: child);
  }
}
