/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-10-08 14:16:19
 */
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/base/controllers/base_refresh_controller.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/behavior/over_scroll_behavior.dart';

///刷新状态
enum Refresh {
  ///初次进入页面加载
  first,

  ///上拉加载
  pull,

  ///下拉加载
  down,
}

// ignore: must_be_immutable
class RefreshWidget<Controller extends BaseRefreshController>
    extends GetView<Controller> {
  RefreshWidget({
    super.key,
    this.enablePullUp = true,
    this.enablePullDown = true,
    this.onRefresh,
    this.onLoadMore,
    this.refreshController,
    this.controllerTag,
    required this.child,
  });

  @override
  String? get tag => controllerTag ?? "";

  ///解决PageView下Controller查找问题
  String? controllerTag;

  ///是否启用上拉
  bool enablePullUp = true;

  ///是否启用下拉
  bool enablePullDown = true;

  ///下拉刷新回调
  VoidCallback? onRefresh;

  ///上拉加载回调
  VoidCallback? onLoadMore;

  ///外部传参RefreshController
  RefreshController? refreshController;

  ///子类，必须是ListView-ScrolleView等
  Widget? child;

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
        behavior: OverScrollBehavior(),
        child: SmartRefresher(
            key: key,
            controller: refreshController ?? controller.refreshController,
            enablePullDown: enablePullDown,
            enablePullUp: enablePullUp,
            onRefresh: () => controller.onLoadRefresh(),
            onLoading: () => controller.onLoadMore(),
            header: ClassicHeader(
              releaseText: ID.releaseText.tr,
              refreshingText: ID.refreshingText.tr,
              completeText: ID.completeText.tr,
              idleText: ID.idleText.tr,
              textStyle: TextStyle(
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                  color: Get.theme.textSecondary,
                  overflow: TextOverflow.ellipsis),
            ),
            footer: ClassicFooter(
              loadingText: ID.loadingText.tr,
              idleText: ID.pullUpToLoad.tr,
              canLoadingText: ID.canLoadingText.tr,
              textStyle: TextStyle(
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                  color: Get.theme.textSecondary,
                  overflow: TextOverflow.ellipsis),
              noDataText: ID.noDataText.tr,
            ),
            child: child));
  }
}
