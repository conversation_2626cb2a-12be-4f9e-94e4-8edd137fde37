import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class RefreshConfig extends StatelessWidget {
  final Widget? child;

  const RefreshConfig({super.key, @required this.child});

  @override
  Widget build(BuildContext context) {
    return RefreshConfiguration(
      footerTriggerDistance: 15,
      dragSpeedRatio: 0.9,
      springDescription: const SpringDescription(
        mass: 1.0,
        stiffness: 150,
        damping: 16.0,
      ),
      headerBuilder: () => const ClassicHeader(),
      // 配置默认头部指示器,假如你每个页面的头部指示器都一样的话,你需要设置这个
      footerBuilder: () => const ClassicFooter(),
      // 配置默认底部指示器
      enableLoadingWhenNoData: false,
      enableRefreshVibrate: false,
      //下拉刷新振动
      enableLoadMoreVibrate: false,
      //上拉加载振动
      hideFooterWhenNotFull: false,
      // Viewport不满一屏时,禁用上拉加载更多功能
      enableBallisticLoad: true,
      // 可以通过惯性滑动触发加载更多
      shouldFooterFollowWhenNotFull: (state) {
        // If you want load more with noMoreData state ,may be you should return false
        return false;
      },
      child: child!,
    );
  }
}
