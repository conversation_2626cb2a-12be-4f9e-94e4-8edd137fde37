import 'dart:math';

import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:big_decimal/big_decimal.dart';
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_controller.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/utils/decimal_utils.dart';

class SegmentControlWidget extends StatefulWidget {
  final double? height;
  final Color? backgroundColor;
  final Color? indicatorColor;
  final Color? borderColor;
  final double borderWidth;
  final Color? textColor;
  final Color? textSelectedColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final String? fontFamily;
  final List<String> values;
  final SegmentedController controller;
  final Function(int index)? onChanged;
  const SegmentControlWidget({
    super.key,
    required this.values,
    this.height,
    this.backgroundColor,
    this.indicatorColor,
    this.borderColor,
    this.borderWidth = 1,
    this.onChanged,
    this.textColor,
    this.textSelectedColor,
    this.fontSize,
    this.fontWeight,
    this.fontFamily,
    required this.controller,
  });

  @override
  State<SegmentControlWidget> createState() => _SegmentedControlWidgetState();
}

class _SegmentedControlWidgetState extends State<SegmentControlWidget> {
  double height = 0;
  Color backgroundColor = Get.theme.colorF3F3F5;
  double indicatorBorderWidth = Get.setWidth(2);
  TextStyle textStyle = const TextStyle();
  int selectedIndex = 0;
  final double minWidth = Get.setWidth(60);
  double indicatorWidth = 0;

  List<int> values = [];

  @override
  void initState() {
    selectedIndex = widget.controller.selectedIndex.value;

    ever(widget.controller.selectedIndex, (value) {
      selectedIndex = value;
      setState(() {});
    });

    textStyle = TextStyle(
      fontSize: widget.fontSize ?? Get.setFontSize(16),
      fontWeight: widget.fontWeight ?? FontWeightX.medium,
      fontFamily: widget.fontFamily,
      color: Color.lerp(
        widget.textColor ?? Get.theme.textSecondary,
        widget.textSelectedColor ?? Get.theme.white,
        0,
      ),
    );

    height = widget.height ?? Get.setHeight(44);
    backgroundColor = widget.backgroundColor ?? backgroundColor;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    int index = 0;
    double maxWidth = minWidth;
    for (var e in widget.values) {
      values.add(index);
      index++;

      textPainter.text = TextSpan(text: e, style: textStyle);

      textPainter.layout();

      maxWidth = max(maxWidth, textPainter.width);
    }

    maxWidth = double.parse(DecimalUtils.decimal(maxWidth.toString(), 2,
        roundingMode: RoundingMode.UP));
    // 加上边距
    maxWidth = maxWidth + 2 * Get.setPaddingSize(24);
    indicatorWidth = maxWidth;

    if (indicatorWidth < minWidth) {
      indicatorWidth = minWidth;
    }

    if (indicatorWidth * values.length >
        Get.width - 2 * Get.setPaddingSize(16)) {
      indicatorWidth =
          (Get.width - 2 * Get.setPaddingSize(16) - 10) / values.length;
    }

    PageController();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedToggleSwitch<int>.size(
        current: selectedIndex,
        height: height,
        style: ToggleStyle(
          backgroundColor: backgroundColor,
          indicatorColor: widget.indicatorColor ?? Get.theme.textPrimary,
          borderColor: widget.borderColor,
          borderRadius: BorderRadius.circular(height / 2),
          indicatorBorder:
              Border.all(color: backgroundColor, width: indicatorBorderWidth),
          indicatorBorderRadius:
              BorderRadius.circular((height - 2 * indicatorBorderWidth) / 2),
        ),
        borderWidth: widget.borderWidth,
        values: values,
        iconOpacity: 1.0,
        selectedIconScale: 1.0,
        animationDuration: const Duration(milliseconds: 300),
        indicatorSize: Size.fromWidth(indicatorWidth),
        iconAnimationType: AnimationType.none,
        styleAnimationType: AnimationType.none,
        spacing: 0,
        customIconBuilder: (context, local, global) {
          return Center(
            child: Text(
              widget.values[local.index],
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: textStyle.fontSize,
                fontWeight: textStyle.fontWeight,
                fontFamily: textStyle.fontFamily,
                color: Color.lerp(
                  widget.textColor ?? Get.theme.textSecondary,
                  widget.textSelectedColor ?? Get.theme.white,
                  local.animationValue,
                ),
              ),
            ),
          );
        },
        onChanged: (index) {
          selectedIndex = index;
          if (widget.onChanged != null) {
            widget.onChanged!(index);
          }
          widget.controller.selectedIndex.value = index;
          setState(() {});
        });
  }
}

class SegmentedController extends BaseController {
  @override
  void loadData() {}

  RxInt selectedIndex;

  SegmentedController({required this.selectedIndex});
}
