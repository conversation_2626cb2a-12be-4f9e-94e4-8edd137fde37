import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer_animation/shimmer_animation.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/colors.dart';
import 'package:vcb/widgets/skeleton/skeleton_widget.dart';

class ListSkeletonItems extends StatelessWidget {
  const ListSkeletonItems({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
        itemCount: 10,
        itemBuilder: (_, index) {
          return Shimmer(
              child: Container(
            margin: EdgeInsets.only(top: Get.setWidth(10)),
            width: Get.width - 36,
            height: Get.width - 36 + 68,
            decoration: BoxDecoration(
              color: Get.theme.colorF3F3F5,
              borderRadius: BorderRadius.all(
                Radius.circular(Get.setRadius(4)),
              ),
            ),
          ));
        });
  }
}

class GridSkeletonItems extends StatelessWidget {
  const GridSkeletonItems({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    double gridWidth = (Get.width - 50) / 2;

    return GridView.builder(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setWidth(20), vertical: Get.setWidth(10)),
        gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: gridWidth,
            crossAxisSpacing: 10.0,
            mainAxisSpacing: 10.0,
            childAspectRatio: gridWidth / (gridWidth + 68)),
        itemCount: 10,
        itemBuilder: (context, index) {
          return Shimmer(
            child: Container(
              decoration: BoxDecoration(
                color: Get.theme.colorF3F3F5,
                borderRadius:
                    BorderRadius.all(Radius.circular(Get.setWidth(16))),
              ),
            ),
          );
        });
  }
}

class HomeTokeListSkeletonWidget extends StatelessWidget {
  const HomeTokeListSkeletonWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      physics: const NeverScrollableScrollPhysics(), // 确保ListView总是可以滚动
      itemCount: 15,
      itemBuilder: (_, index) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(14)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(right: Get.setPaddingSize(10)),
              child: SkeletonItems(
                radius: Get.setRadius(100),
                width: Get.setWidth(28),
                height: Get.setWidth(28),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  children: [
                    SkeletonItems(
                      radius: Get.setRadius(4),
                      width: Get.width - Get.setWidth(72),
                      height: DefaultTextStyle.of(context).style.fontSize! * .8,
                    ),
                  ],
                ),
                SizedBox(height: Get.setHeight(4)),
                SkeletonItems(
                  radius: 4,
                  width: Get.width / 2 - 28,
                  height: DefaultTextStyle.of(context).style.fontSize! * .8,
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
