/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-04 10:41:41
 * @LastEditTime: 2024-10-08 10:17:01
 */
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shimmer_animation/shimmer_animation.dart';
import 'package:vcb/res/colors.dart';

class SkeletonItems extends StatelessWidget {
  final double? width;
  final double? height;
  final double? radius;

  const SkeletonItems({
    super.key,
    this.width,
    this.height,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(radius ?? 0),
        child: Shimmer(
            child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Get.theme.colorF3F3F5,
            borderRadius: BorderRadius.all(Radius.circular(radius ?? 0)),
          ),
        )));
  }
}
