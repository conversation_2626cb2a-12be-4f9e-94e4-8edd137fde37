/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-26 17:50:28
 */

import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/button/button_widget.dart';
import 'package:vcb/widgets/button/outlined_button_widget.dart';

class ActionButtonWidget extends StatelessWidget {
  const ActionButtonWidget({
    super.key,
    this.leftText,
    this.rightText,
    this.onLeftPressed,
    this.onRightPressed,
  });

  final String? leftText;
  final String? rightText;
  final VoidCallback? onLeftPressed;
  final VoidCallback? onRightPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: OutlinedButtonWidget(
                text: leftText ?? ID.stringDeny.tr, onPressed: onLeftPressed),
          ),
          SizedBox(width: Get.setWidth(10)),
          Expanded(
            child: ButtonWidget(
                text: rightText ?? ID.stringConfirm.tr,
                onPressed: onRightPressed),
          ),
        ],
      ),
    );
  }
}
