/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-26 15:25:10
 */
import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/button/button_widget.dart';

class OutlinedButtonWidget extends StatelessWidget {
  const OutlinedButtonWidget(
      {super.key,
      @required this.text,
      this.textStyle,
      this.onPressed,
      this.buttonSize = ButtonSize.normal,
      this.buttonStatus = ButtonStatus.enable,
      this.width,
      this.height,
      this.child});

  final VoidCallback? onPressed;
  final String? text;
  final TextStyle? textStyle;
  final Widget? child;
  final ButtonSize? buttonSize;
  final ButtonStatus? buttonStatus;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: buttonStatus == ButtonStatus.loading ||
              buttonStatus == ButtonStatus.disable
          ? null
          : onPressed,
      style: OutlinedButton.styleFrom(
        backgroundColor: (buttonStatus == ButtonStatus.disable)
            ? Get.theme.colorECECEC
            : Get.theme.bgColor,
        side: buttonStatus == ButtonStatus.disable
            ? BorderSide.none
            : BorderSide(
                color: Get.theme.colorD3D3D3, width: Get.setWidth(0.5)),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(Get.setRadius(30))),
        minimumSize: buttonSize == ButtonSize.normal
            ? Size(width ?? 0, height ?? Get.setHeight(44))
            : Size(width ?? Get.width, height ?? Get.setHeight(44)),
      ),
      child: child ??
          Text(text ?? "",
              style: textStyle ??
                  TextStyle(
                      fontSize: Get.setFontSize(16),
                      color: buttonStatus == ButtonStatus.disable
                          ? Get.theme.textTertiary
                          : Get.theme.textPrimary,
                      fontFamily: Get.setFontFamily(),
                      fontWeight: FontWeightX.medium)),
    );
  }
}
