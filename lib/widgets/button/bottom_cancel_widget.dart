/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-13 13:07:40
 * @LastEditTime: 2025-08-04 13:10:27
 */

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';
import 'package:vcb/widgets/button/high_light_tab.dart';
import 'package:vcb/widgets/divider/divider_widget.dart';

class BottomCancelWidget extends StatelessWidget {
  const BottomCancelWidget(
      {super.key, this.hideDivider = false, this.cancelTitle, this.onTap});

  final String? cancelTitle;
  final VoidCallback? onTap;

  /// 隐藏线
  final bool hideDivider;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Visibility(visible: !hideDivider, child: const DividerWidget()),
        HighLightInkWell(
          onTap: onTap ?? () => Get.back(),
          child: Padding(
            padding: EdgeInsets.only(
                top: Get.setHeight(15),
                bottom: ScreenUtil().bottomBarHeight == 0
                    ? Get.setHeight(15)
                    : ScreenUtil().bottomBarHeight),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  cancelTitle ?? ID.stringCancel.tr,
                  style: textPrimary16Medium,
                  textAlign: TextAlign.center,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
