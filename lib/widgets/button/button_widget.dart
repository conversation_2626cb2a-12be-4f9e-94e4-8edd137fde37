import 'package:flutter/material.dart';
import 'package:vcb/base/controllers/base_get_extension.dart';
import 'package:vcb/res/resource.dart';

enum ButtonShape { capsule, rectangle } //默认胶囊按钮 ，矩形按钮

enum ButtonStatus { enable, disable, loading, icon } //默认可点击状态，不可点击状态,加载状态

enum ButtonSize { full, normal } //宽度充满屏幕 ，自适应宽度

class ButtonWidget extends StatelessWidget {
  const ButtonWidget(
      {super.key,
      required this.text,
      this.onPressed,
      this.buttonShape = ButtonShape.capsule,
      this.buttonStatus = ButtonStatus.enable,
      this.buttonSize = ButtonSize.normal,
      this.textStyle,
      this.width,
      this.height,
      this.iconWidget,
      this.radius});

  final String? text;
  final VoidCallback? onPressed;
  final ButtonShape buttonShape;
  final TextStyle? textStyle;
  final double? radius;
  final ButtonStatus? buttonStatus;
  final ButtonSize? buttonSize;
  final double? width;
  final double? height;
  final Widget? iconWidget;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        onPressed: buttonStatus == ButtonStatus.loading ||
                buttonStatus == ButtonStatus.disable
            ? null
            : onPressed,
        style: ButtonStyle(
          backgroundColor:
              WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (buttonStatus == ButtonStatus.disable &&
                buttonStatus != ButtonStatus.loading) {
              return Get.theme.colorECECEC;
            } else if (states.contains(WidgetState.pressed)) {
              return Get.theme.buttonFocusColor;
            }
            return Get.theme.primary;
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color?>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return Get.theme.colorF5454545;
              }

              return Get.theme.bgColor;
            },
          ),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            buttonShape == ButtonShape.capsule
                ? RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30.0))
                : RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(radius ?? 0)),
          ),
          fixedSize: WidgetStateProperty.all(buttonSize == ButtonSize.normal
              ? Size(width ?? 0, height ?? Get.setHeight(44))
              : Size(width ?? Get.width, height ?? Get.setHeight(44))),
        ),
        child: (buttonStatus == ButtonStatus.loading ||
                buttonStatus == ButtonStatus.icon)
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Visibility(
                      visible: (buttonStatus == ButtonStatus.loading ||
                          buttonStatus == ButtonStatus.icon),
                      child: buttonStatus == ButtonStatus.loading
                          ? Padding(
                              padding:
                                  EdgeInsets.only(right: Get.setPaddingSize(8)),
                              child: SizedBox(
                                width: Get.setImageSize(14),
                                height: Get.setImageSize(14),
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Get.theme.bgColor),
                                ),
                              ),
                            )
                          : (iconWidget ?? const SizedBox.shrink())),
                  Text(text ?? "",
                      style: textStyle ??
                          TextStyle(
                              fontSize: Get.setFontSize(16),
                              color: buttonStatus == ButtonStatus.disable
                                  ? Get.theme.textTertiary
                                  : Get.theme.bgColor,
                              fontFamily: Get.setFontFamily(),
                              fontWeight: FontWeightX.medium))
                ],
              )
            : Text(text ?? "",
                style: textStyle ??
                    TextStyle(
                        fontSize: Get.setFontSize(16),
                        color: buttonStatus == ButtonStatus.disable
                            ? Get.theme.textTertiary
                            : Get.theme.bgColor,
                        fontFamily: Get.setFontFamily(),
                        fontWeight: FontWeightX.medium)));
  }
}
