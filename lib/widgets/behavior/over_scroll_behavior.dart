import 'package:flutter/material.dart';

class OverScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and properties as needed.

  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    // This method controls the overscroll indicator.
    // Return the child without any overscroll indicator.
    return child;
  }

  @override
  Widget buildScrollbar(
      BuildContext context, Widget child, ScrollableDetails details) {
    // If you want to remove the scrollbar as well, return the child directly.
    // Otherwise, return the super method to keep the scrollbar.
    return child;
  }
}
