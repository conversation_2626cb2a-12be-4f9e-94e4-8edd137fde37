# Passkey 后端接口清单（包含设备指纹）

## 概述

基于你的代码结构，后端需要提供以下5个接口，所有接口都需要处理设备指纹信息。

## 设备信息说明

前端会提供以下设备信息：
- **deviceId**: 设备唯一标识符
- **deviceName**: 设备名称
- **fingerprint**: 设备指纹（基于deviceId和deviceName生成）

## 接口清单

### 1. 注册初始化
```
POST /api/passkey/register/init
```
**请求头**:
```
X-Device-Fingerprint: {fingerprint}
```
**请求体**:
```json
{
  "email": "<EMAIL>"
}
```
**响应**:
```json
{
  "webauthnOptions": {
    "challenge": "...",
    "rp": {"id": "domain.com", "name": "App Name"},
    "user": {"id": "...", "name": "...", "displayName": "..."},
    "pubKeyCredParams": [...],
    "timeout": 60000
  }
}
```

### 2. 注册完成
```
POST /api/passkey/register/finish
```
**请求头**:
```
X-Device-Fingerprint: {fingerprint}
```
**请求体**:
```json
{
  "email": "<EMAIL>",
  "signedPasskeyData": {
    "id": "credential_id",
    "response": {"attestationObject": "...", "clientDataJSON": "..."}
  }
}
```
**响应**:
```json
{
  "success": true,
  "accessToken": "jwt_token",
  "refreshToken": "refresh_token",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "displayName": "User Name"
  }
}
```

### 3. 登录初始化
```
POST /api/passkey/login/init
```
**请求头**:
```
X-Device-Fingerprint: {fingerprint}
```
**请求体**:
```json
{
  "email": "<EMAIL>"
}
```
**响应**:
```json
{
  "webauthnOptions": {
    "challenge": "...",
    "allowCredentials": [...],
    "timeout": 60000
  }
}
```

### 4. 登录完成
```
POST /api/passkey/login/finish
```
**请求头**:
```
X-Device-Fingerprint: {fingerprint}
```
**请求体**:
```json
{
  "email": "<EMAIL>",
  "signedPasskeyData": {
    "id": "credential_id",
    "response": {"authenticatorData": "...", "signature": "..."}
  }
}
```
**响应**:
```json
{
  "success": true,
  "accessToken": "jwt_token",
  "refreshToken": "refresh_token",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "displayName": "User Name"
  }
}
```

### 5. 获取管理令牌
```
POST /api/passkey/connect-token
```
**请求头**:
```
Authorization: Bearer {access_token}
X-Device-Fingerprint: {fingerprint}
```
**请求体**:
```json
{
  "action": "passkey-list"
}
```
**响应**:
```json
{
  "connectToken": "connect_token_string"
}
```

## 前端设备信息获取

前端通过以下方式获取设备信息：

```dart
// 在PasskeyAuthController中
final deviceId = ''.obs;
final fingerprint = ''.obs;
final deviceName = ''.obs;

Future<void> _initDeviceInfo() async {
  deviceId.value = await DeviceUtils.getDeviceId() ?? 'unknown_device';
  fingerprint.value = await DeviceUtils.getFingerprint();
  deviceName.value = await DeviceUtils.getDeviceName() ?? 'unknown_device';
}
```

## 后端处理要点

1. **设备指纹验证**：
   - 从请求头 `X-Device-Fingerprint` 获取设备指纹
   - 用于风险评估和设备绑定
   - 记录设备信息用于安全审计

2. **Corbado集成**：
   - 调用Corbado的 `initializeRegistration` 和 `initializeAuthentication`
   - 使用Corbado的 `verifySignedData` 验证签名数据

3. **用户管理**：
   - 创建/查找用户账户
   - 绑定设备信息到用户

4. **令牌管理**：
   - 生成JWT访问令牌和刷新令牌
   - 在令牌中包含设备信息

5. **安全考虑**：
   - 记录设备指纹变化
   - 实现设备信任机制
   - 异常设备检测和告警

## 错误响应格式

```json
{
  "success": false,
  "code": 400,
  "message": "错误描述",
  "error": "ERROR_CODE"
}
```

## 总结

这5个接口涵盖了完整的Passkey认证流程，包含设备指纹信息用于安全增强。前端已经实现了设备信息的自动获取和传递，后端只需要按照这个接口规范实现即可。
