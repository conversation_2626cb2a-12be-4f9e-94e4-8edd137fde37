# 存储服务快速参考

## 🚀 快速选择指南

| 数据类型 | 使用服务 | 原因 |
|---------|---------|------|
| 🔐 访问令牌 | SecureStorageService | 敏感认证信息 |
| 🔐 刷新令牌 | SecureStorageService | 敏感认证信息 |
| 🔐 设备ID | SecureStorageService | 设备唯一标识 |
| 🔐 设备指纹 | SecureStorageService | 安全验证信息 |
| 🔐 用户密码 | SecureStorageService | 高度敏感信息 |
| ⚙️ 主题设置 | StorageManager | 用户偏好配置 |
| ⚙️ 语言设置 | StorageManager | 应用配置 |
| 📝 搜索历史 | StorageManager | 非敏感历史记录 |
| 📝 访问记录 | StorageManager | 用户行为数据 |
| 💾 API缓存 | StorageManager | 临时缓存数据 |
| 👤 用户昵称 | StorageManager | 非敏感用户信息 |
| 👤 用户头像 | StorageManager | 公开用户信息 |

## 📋 简单判断原则

### ✅ 使用 SecureStorageService 当：
- 数据泄露会造成**安全风险**
- 涉及**身份认证**或**设备识别**
- 包含**密码**、**令牌**等敏感信息
- 需要**加密存储**保护

### ✅ 使用 StorageManager 当：
- 数据泄露只影响**用户体验**
- 属于**应用配置**或**用户偏好**
- 是**缓存数据**或**历史记录**
- 需要**高性能**频繁访问

## 🔧 常用代码片段

### SecureStorageService 常用操作

```dart
// 保存令牌
await SecureStorageService.saveAccessToken('your_token');
await SecureStorageService.saveRefreshToken('refresh_token');

// 获取令牌
final token = await SecureStorageService.getAccessToken();
final refreshToken = await SecureStorageService.getRefreshToken();

// 设备信息
await SecureStorageService.saveDeviceId('device_123');
final deviceId = await SecureStorageService.getDeviceId();

// 检查登录状态
final isLoggedIn = await SecureStorageService.isLoggedIn();

// 登出清理
await SecureStorageService.clearAuthData();
```

### StorageManager 常用操作

```dart
// 应用配置
await StorageManager.saveValue(key: 'theme', value: 'dark');
final theme = StorageManager.getValue<String>(key: 'theme');

// 用户偏好
await StorageManager.saveValue(key: 'language', value: 'zh_CN');
final language = StorageManager.getValue<String>(key: 'language') ?? 'en_US';

// 搜索历史（限制10条）
await StorageManager.addStringToListLimit(key: 'search_history', value: '关键词');
final history = StorageManager.getStringReversedList(key: 'search_history');

// 清理数据
await StorageManager.remove(key: 'cache_data');
await StorageManager.clearStringList(key: 'search_history');
```

## ⚠️ 常见错误

### ❌ 错误用法
```dart
// 敏感数据使用普通存储
await StorageManager.saveValue(key: 'access_token', value: token);

// 频繁访问的配置使用安全存储
final theme = await SecureStorageService.getValue('theme'); // 不存在此方法
```

### ✅ 正确用法
```dart
// 敏感数据使用安全存储
await SecureStorageService.saveAccessToken(token);

// 配置数据使用普通存储
final theme = StorageManager.getValue<String>(key: 'theme');
```

## 🎯 最佳实践

1. **安全优先**：有疑问时选择SecureStorageService
2. **性能考虑**：频繁访问的配置用StorageManager
3. **错误处理**：安全存储操作要处理异常
4. **数据清理**：登出时清理认证信息，保留用户偏好

## 📞 快速联系

如果不确定使用哪个服务，问自己：
> "这个数据如果被其他应用看到，会有安全问题吗？"

- **是** → SecureStorageService
- **否** → StorageManager
