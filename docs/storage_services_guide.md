# 存储服务使用指南

## 概述

项目中提供了两个存储服务，分别用于不同类型数据的存储：

1. **SecureStorageService** - 安全存储服务（敏感数据）
2. **StorageManager** - 本地存储管理器（非敏感数据）

## 存储策略

### 🔐 SecureStorageService（敏感数据）

**适用场景：**
- 用户认证令牌（访问令牌、刷新令牌）
- 设备唯一标识（设备ID、设备指纹）
- 密码、密钥等敏感信息

**技术实现：**
- 基于 `FlutterSecureStorage`
- 数据加密存储在系统安全区域
- iOS：存储在Keychain
- Android：使用EncryptedSharedPreferences

**使用示例：**
```dart
// 保存访问令牌
await SecureStorageService.saveAccessToken('your_token');

// 获取访问令牌
final token = await SecureStorageService.getAccessToken();

// 保存设备信息
await SecureStorageService.saveDeviceId('device_123');
await SecureStorageService.saveDeviceFingerprint('fingerprint_abc');

// 检查登录状态
final isLoggedIn = await SecureStorageService.isLoggedIn();

// 清除认证信息（登出）
await SecureStorageService.clearAuthData();
```

### 📦 StorageManager（非敏感数据）

**适用场景：**
- 应用配置和用户偏好
- 缓存数据和临时数据
- 搜索历史和访问记录
- 主题设置、语言设置等

**技术实现：**
- 基于 `GetStorage`
- 高性能的本地存储
- 支持基础类型和自定义对象
- 自动序列化/反序列化

**使用示例：**
```dart
// 保存基础数据
await StorageManager.saveValue(key: 'theme', value: 'dark');
await StorageManager.saveValue(key: 'language', value: 'zh_CN');

// 获取基础数据
final theme = StorageManager.getValue<String>(key: 'theme');
final isFirstLaunch = StorageManager.getValue<bool>(key: 'first_launch') ?? true;

// 保存自定义对象
await StorageManager.saveObject(key: 'user_config', obj: configModel);

// 获取自定义对象
final config = StorageManager.getObject<ConfigModel>(
  key: 'user_config',
  fromJson: ConfigModel.fromJson,
);

// 管理字符串列表（搜索历史）
await StorageManager.addStringToListLimit(key: 'search_history', value: '搜索关键词');
final history = StorageManager.getStringReversedList(key: 'search_history');
```

## 数据分类指南

### 🔴 必须使用SecureStorageService的数据

| 数据类型 | 说明 | 示例 |
|---------|------|------|
| 认证令牌 | 用户身份验证 | accessToken, refreshToken |
| 设备标识 | 设备唯一性验证 | deviceId, deviceFingerprint |
| 密码密钥 | 加密相关信息 | userPassword, encryptionKey |
| 个人敏感信息 | 隐私数据 | idCard, phoneNumber |

### 🟢 可以使用StorageManager的数据

| 数据类型 | 说明 | 示例 |
|---------|------|------|
| 应用配置 | 用户偏好设置 | theme, language, fontSize |
| 缓存数据 | 临时存储数据 | apiCache, imageCache |
| 历史记录 | 用户行为记录 | searchHistory, visitHistory |
| 非敏感用户信息 | 公开的用户信息 | nickname, avatar, preferences |

## 最佳实践

### 1. 数据安全原则

```dart
// ✅ 正确：敏感数据使用安全存储
await SecureStorageService.saveAccessToken(token);

// ❌ 错误：敏感数据使用普通存储
await StorageManager.saveValue(key: 'token', value: token);
```

### 2. 性能考虑

```dart
// ✅ 正确：频繁访问的配置数据使用StorageManager
final theme = StorageManager.getValue<String>(key: 'theme');

// ✅ 正确：安全数据在需要时才访问
final token = await SecureStorageService.getAccessToken();
```

### 3. 错误处理

```dart
// ✅ 正确：处理可能的异常
try {
  final token = await SecureStorageService.getAccessToken();
  if (token != null) {
    // 使用token
  }
} catch (e) {
  // 处理错误
  Log.e('获取令牌失败: $e');
}
```

### 4. 数据清理

```dart
// 用户登出时
await SecureStorageService.clearAuthData(); // 清除认证信息
// 保留应用配置和历史记录

// 应用重置时
await SecureStorageService.clearAllData(); // 清除所有安全数据
await StorageManager.remove(key: 'user_preferences'); // 清除特定配置
```

## 常见问题

### Q: 什么时候使用哪个存储服务？

**A:** 简单判断原则：
- 如果数据泄露会造成安全风险 → SecureStorageService
- 如果数据只是影响用户体验 → StorageManager

### Q: 可以在两个服务之间迁移数据吗？

**A:** 可以，但需要注意：
```dart
// 从普通存储迁移到安全存储
final oldData = StorageManager.getValue<String>(key: 'sensitive_data');
if (oldData != null) {
  await SecureStorageService.saveAccessToken(oldData);
  await StorageManager.remove(key: 'sensitive_data');
}
```

### Q: 如何处理存储失败？

**A:** 两个服务都有内置的错误处理和日志记录，但建议在关键操作时添加额外的错误处理：
```dart
try {
  await SecureStorageService.saveAccessToken(token);
} catch (e) {
  // 备用方案或用户提示
  showErrorDialog('保存失败，请重试');
}
```

## 总结

- **SecureStorageService**：安全第一，用于敏感数据
- **StorageManager**：性能优先，用于配置和缓存
- 根据数据的敏感性选择合适的存储服务
- 遵循最佳实践，确保数据安全和应用性能
