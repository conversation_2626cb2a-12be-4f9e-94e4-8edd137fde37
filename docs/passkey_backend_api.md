# Passkey 后端接口文档 - Corbado 集成

## 概述

基于 Corbado 的 Passkey 认证流程，后端需要提供以下接口来支持前端的注册、登录和管理功能。

## 流程图

```
注册流程：
Flutter → /register/init → Corbado initializeRegistration → 返回 options
Flutter 调用 corbadoAuth.signUp() → signedPasskeyData
Flutter → /register/finish → Corbado verifySignedData → 本地创建用户 & 会话

登录流程：
Flutter → /login/init → Corbado initializeAuthentication → 返回 options
Flutter 调用 corbadoAuth.signIn() → signedPasskeyData
Flutter → /login/finish → Corbado verifySignedData → 本地创建会话

Passkey 管理：
Flutter → /connect-token (action) → Corbado createConnectToken
Flutter 把 connectToken 交给 Corbado SDK 或 Web UI → 执行 append/list/delete
```

## 接口详情

### 1. 注册初始化接口

**POST** `/api/register/init`

**描述**: 调用 Corbado initializeRegistration API 获取注册选项

**请求参数**:

```json
{
  "email": "<EMAIL>"
}
```

**请求头**:

```
X-Device-Fingerprint: device_fingerprint_string
```

**后端处理**:

```javascript
// 调用 Corbado Backend API
const response = await corbado.initializeRegistration({
  userID: generateUserID(email),
  username: email
});
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // Corbado 返回的注册选项
    "challenge": "base64url编码的随机挑战",
    "rp": {
      "id": "your-domain.com",
      "name": "VCB App"
    },
    "user": {
      "id": "base64url编码的用户ID",
      "name": "<EMAIL>",
      "displayName": "用户昵称"
    },
    "pubKeyCredParams": [...],
    "timeout": 60000,
    "attestation": "direct"
  }
}
```

### 2. 注册完成接口

**POST** `/api/register/finish`

**描述**: 验证 signedPasskeyData 并创建用户账户

**请求参数**:

```json
{
  "email": "<EMAIL>",
  "signedPasskeyData": {
    // Corbado SDK 返回的签名数据
    "id": "credential_id",
    "rawId": "base64url编码的原始ID",
    "response": {
      "attestationObject": "...",
      "clientDataJSON": "..."
    },
    "type": "public-key"
  }
}
```

**后端处理**:

```javascript
// 调用 Corbado Backend API 验证
const verifyResult = await corbado.verifySignedData(signedPasskeyData);

if (verifyResult.success) {
  // 创建本地用户账户
  const user = await createUser(email);
  // 创建会话
  const token = generateJWT(user);
  return { success: true, token, user };
}
```

**响应**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "success": true,
    "token": "JWT访问令牌",
    "user": {
      "id": "用户ID",
      "email": "<EMAIL>",
      "displayName": "用户昵称"
    }
  }
}
```

### 3. 登录初始化接口

**POST** `/api/login/init`

**描述**: 调用 Corbado initializeAuthentication API 获取登录选项

**请求参数**:

```json
{
  "email": "<EMAIL>"
}
```

**后端处理**:

```javascript
// 调用 Corbado Backend API
const response = await corbado.initializeAuthentication({
  username: email
});
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // Corbado 返回的登录选项
    "challenge": "base64url编码的随机挑战",
    "timeout": 60000,
    "rpId": "your-domain.com",
    "allowCredentials": [...],
    "userVerification": "required"
  }
}
```

### 4. 登录完成接口

**POST** `/api/login/finish`

**描述**: 验证 signedPasskeyData 并建立会话

**请求参数**:

```json
{
  "email": "<EMAIL>",
  "signedPasskeyData": {
    // Corbado SDK 返回的签名数据
    "id": "credential_id",
    "rawId": "base64url编码的原始ID",
    "response": {
      "authenticatorData": "...",
      "clientDataJSON": "...",
      "signature": "..."
    },
    "type": "public-key"
  }
}
```

**后端处理**:

```javascript
// 调用 Corbado Backend API 验证
const verifyResult = await corbado.verifySignedData(signedPasskeyData);

if (verifyResult.success) {
  // 查找用户
  const user = await findUserByEmail(email);
  // 创建会话
  const token = generateJWT(user);
  return { success: true, token, user };
}
```

**响应**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "success": true,
    "token": "JWT访问令牌",
    "user": {
      "id": "用户ID",
      "email": "<EMAIL>",
      "displayName": "用户昵称"
    }
  }
}
```

### 5. 获取ConnectToken接口

**POST** `/api/connect-token`

**描述**: 获取用于 Passkey 管理的 ConnectToken

**请求头**:

```
Authorization: Bearer {JWT_TOKEN}
```

**请求参数**:

```json
{
  "action": "passkey-list" // 或 "passkey-append", "passkey-delete"
}
```

**后端处理**:

```javascript
// 调用 Corbado Backend API
const response = await corbado.createConnectToken({
  userID: user.corbadoUserID,
  action: action
});
```

**响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "connectToken": "corbado_connect_token_string",
    "expiresIn": 300
  }
}
```

## 错误处理

所有接口都应该返回统一的错误格式：

```json
{
  "code": 400,
  "message": "错误描述",
  "error": "ERROR_CODE"
}
```

## 环境配置

后端需要配置以下 Corbado 相关环境变量：

```env
CORBADO_PROJECT_ID=your_corbado_project_id
CORBADO_API_SECRET=your_corbado_api_secret
CORBADO_FRONTEND_API_URL=https://your-project.frontendapi.corbado.io
CORBADO_BACKEND_API_URL=https://your-project.backendapi.corbado.io
```

## 注意事项

1. 所有接口都需要使用 HTTPS
2. 验证 signedPasskeyData 时要检查 origin 和 rpId
3. 实现适当的速率限制
4. 记录审计日志
5. 处理 Corbado API 的异常情况
