# Passkey + 密码 + OTP 完整认证方案

## 概述

我们为 example 下的 PasskeyAuthController 添加了完整的认证功能：

1. **Passkey 认证**：主要认证方式，使用生物识别
2. **密码备用**：当 Passkey 不可用时的备用方案
3. **邮箱 OTP**：额外的安全验证层

## 🔧 新增功能

### 1. 认证模式选择

```dart
// 控制器中的状态
final usePasskeyOnly = true.obs;  // true: 仅Passkey, false: Passkey + 密码
final requireOtpVerification = false.obs;  // 是否需要OTP验证
```

### 2. 密码功能

```dart
// 密码相关状态
final password = ''.obs;

// 密码登录方法
Future<void> loginWithPassword() async {
  // 直接使用密码登录，不使用Passkey
}
```

### 3. OTP 验证功能

```dart
// OTP 相关状态
final otpCode = ''.obs;
final isOtpSent = false.obs;
final otpCountdown = 0.obs;
final canResendOtp = true.obs;

// OTP 方法
Future<void> sendEmailOtp() async;     // 发送验证码
Future<bool> verifyEmailOtp() async;   // 验证验证码
```

## 🎯 使用场景

### 场景1：纯 Passkey 模式（推荐）
- 用户只需要邮箱 + 生物识别
- 最安全，用户体验最佳
- 适合支持 Passkey 的现代设备

### 场景2：Passkey + 密码模式
- Passkey 为主，密码为备用
- 兼容性更好，适合各种设备
- 用户可以选择使用哪种方式登录

### 场景3：需要 OTP 验证
- 高安全要求的场景
- 新设备首次登录
- 异常登录检测时

## 📱 UI 功能

### 1. 认证模式切换
```dart
CheckboxListTile(
  title: Text('仅使用 Passkey'),
  subtitle: Text('更安全，无需密码'),
  value: controller.usePasskeyOnly.value,
  onChanged: (value) {
    controller.usePasskeyOnly.value = value ?? true;
  },
)
```

### 2. 密码输入（条件显示）
```dart
Obx(() => !controller.usePasskeyOnly.value
    ? TextField(
        decoration: InputDecoration(
          hintText: '密码（备用）- 测试：123456',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.lock),
        ),
        obscureText: true,
        onChanged: (value) {
          controller.password.value = value;
        },
      )
    : SizedBox())
```

### 3. OTP 功能区域
- 发送验证码按钮（带倒计时）
- OTP 输入框
- 验证按钮

## 🔌 后端接口

### 新增的 OTP 接口

```dart
// 发送邮箱 OTP
static Future<Map<String, dynamic>> sendEmailOtp(
  String email,
  String deviceFingerprint,
) async;

// 验证邮箱 OTP
static Future<Map<String, dynamic>> verifyEmailOtp(
  String email,
  String otpCode,
  String deviceFingerprint,
) async;

// 密码登录
static Future<Map<String, dynamic>> loginWithPassword(
  String email,
  String password,
  String deviceFingerprint,
) async;
```

### 测试数据

为了方便测试，我们提供了模拟响应：

- **测试密码**：`123456`
- **测试 OTP**：`123456`
- **邮箱**：任意有效邮箱格式

## 🚀 完整流程示例

### 注册流程

1. **输入邮箱**
2. **选择认证模式**：
   - 纯 Passkey：只需生物识别
   - Passkey + 密码：输入备用密码
3. **（可选）OTP 验证**：
   - 发送验证码到邮箱
   - 输入验证码验证
4. **Passkey 注册**：
   - 调用 Corbado SDK
   - 生物识别验证
   - 创建 Passkey
5. **完成注册**

### 登录流程

#### 方式1：Passkey 登录
1. 输入邮箱
2. 点击"登录 Passkey"
3. 生物识别验证
4. 登录成功

#### 方式2：密码登录
1. 输入邮箱和密码
2. 点击"密码登录"
3. 登录成功

#### 方式3：OTP 验证
1. 输入邮箱
2. 点击"发送验证码"
3. 输入收到的验证码
4. 点击"验证"
5. 验证成功后可进行其他操作

## 🔒 安全特性

### 1. 密码加密
- 使用 RSA 公钥加密密码
- 前端不存储明文密码
- 传输过程中密码已加密

### 2. 设备指纹
- 每个请求都包含设备指纹
- 用于风险评估和设备绑定
- 异常设备检测

### 3. OTP 安全
- 验证码有效期限制（5分钟）
- 发送频率限制（60秒倒计时）
- 防止暴力破解

### 4. 多层验证
- Passkey（生物识别）
- 密码（知识因子）
- OTP（邮箱验证）
- 设备指纹（设备因子）

## 📋 后端需要实现的接口

### 1. OTP 相关
```
POST /api/auth/send-otp
POST /api/auth/verify-otp
```

### 2. 密码登录
```
POST /api/auth/login-password
```

### 3. 更新现有接口
- 注册和登录接口支持可选的密码参数
- 所有接口支持设备指纹验证

## 🎉 总结

现在的 PasskeyAuthController 提供了完整的认证解决方案：

- ✅ **Passkey 认证**：现代化的无密码认证
- ✅ **密码备用**：传统密码作为备用方案
- ✅ **OTP 验证**：邮箱验证码增强安全性
- ✅ **设备指纹**：设备识别和风险评估
- ✅ **灵活配置**：支持多种认证模式组合
- ✅ **用户友好**：清晰的 UI 和状态提示

这个方案既保证了安全性，又提供了良好的用户体验和设备兼容性！
