# Passkey调试指南

## 🔍 当前问题

**添加Passkey时没有弹出生物识别界面**

## 🧪 调试版本已部署

我已经添加了多层调试和多种尝试方法：

### 方法1：直接调用appendPasskey()
```dart
await corbadoAuth.appendPasskey();
```

### 方法2：检查认证流程状态
```dart
final currentUser = await corbadoAuth.currentUser;
await corbadoAuth.appendPasskey();
```

### 方法3：尝试session方式
```dart
await _trySessionAppendPasskey();
```

### 方法4：直接使用passkeys包
```dart
await _tryDirectPasskeyCreation();
```

## 📱 测试步骤

### 1. 运行注册流程
1. 输入邮箱+密码 `123456`
2. OTP验证：输入 `123456`
3. 点击"创建Passkey"
4. **观察控制台日志**

### 2. 关键日志信息
查看以下日志输出：

```
🚀 开始Passkey注册: <EMAIL>
📋 WebAuthn选项: {...}
🔍 检查CorbadoAuth状态...
CorbadoAuth实例: ...
CorbadoAuth类型: ...
项目ID: ...
尝试方法1: 直接调用appendPasskey()
```

### 3. 可能的结果分析

#### 情况A：方法1成功
```
方法1成功 - appendPasskey()完成
```
**说明**：Corbado工作正常，但可能没有拉起UI
**下一步**：检查设备权限和配置

#### 情况B：方法1失败，显示具体错误
```
方法1失败: [具体错误信息]
尝试方法2: 初始化认证流程后调用
```
**说明**：需要分析具体错误
**常见错误**：
- `404`：后端API不可用
- `Process not found`：认证流程未初始化
- `No authenticator`：设备不支持

#### 情况C：所有方法都失败
```
方法4也失败: [错误信息]
使用模拟Passkey注册数据
```
**说明**：Corbado集成有问题，使用fallback
**结果**：应用继续正常工作

## 🔧 可能的解决方案

### 1. 检查Corbado初始化
```dart
// 确认项目ID是否正确
Log.logPrint("项目ID: ${corbadoAuth.projectId}");

// 确认是否正确初始化
await corbadoAuth.init(projectId: "your-project-id");
```

### 2. 检查设备权限
- **iOS**：确认Face ID/Touch ID权限
- **Android**：确认生物识别权限
- **设备支持**：确认设备有生物识别硬件

### 3. 检查网络和后端
- Corbado后端服务是否可用
- 网络连接是否正常
- API端点是否正确

### 4. 使用Corbado组件
可能需要使用Corbado的UI组件而不是直接调用API：
```dart
// 可能需要使用CorbadoAuthComponent
// 让Corbado管理整个UI流程
```

## 📋 调试清单

请运行测试并提供以下信息：

- [ ] **项目ID**：日志中显示的项目ID
- [ ] **错误信息**：具体的错误消息
- [ ] **设备信息**：iOS/Android版本
- [ ] **权限状态**：生物识别权限是否授予
- [ ] **网络状态**：是否能访问Corbado服务

## 🎯 预期行为

### 正常情况下应该发生：
1. 调用 `corbadoAuth.appendPasskey()`
2. Corbado调用后端获取challenge
3. 调用 `passkeyAuthenticator.register()`
4. **弹出系统生物识别界面**
5. 用户验证后创建Passkey
6. 发送结果到后端完成注册

### 如果没有弹出界面：
- 可能在步骤2失败（后端问题）
- 可能在步骤3失败（设备问题）
- 可能在步骤4失败（权限问题）

## 🚀 下一步

**请运行测试并提供详细的日志输出**，特别是：

1. **CorbadoAuth状态信息**
2. **具体的错误消息**
3. **哪个方法失败了**
4. **设备和权限信息**

根据这些信息，我们可以精确定位问题并提供解决方案！

## 💡 临时解决方案

如果Corbado集成复杂，我们可以：
1. **继续使用当前的模拟实现**
2. **专注于完善其他功能**
3. **等待Corbado技术支持**
4. **考虑使用其他Passkey解决方案**

但首先让我们尝试调试当前的实现！🔍
