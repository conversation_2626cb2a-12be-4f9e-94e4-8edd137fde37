# 新的认证流程：邮箱+密码 → OTP → Passkey

## 🔄 完整流程

### 注册流程
1. **主页面**：输入邮箱 + 密码 → 点击"注册账户"
2. **OTP验证页面**：自动发送验证码 → 输入验证码 → 点击"验证并继续"
3. **Passkey创建页面**：生物识别验证 → 创建Passkey → 注册完成

### 登录流程
1. **主页面**：输入邮箱 + 密码 → 点击"登录账户"
2. **OTP验证页面**：自动发送验证码 → 输入验证码 → 点击"验证并继续"
3. **Passkey认证页面**：生物识别验证 → 使用Passkey → 登录完成

## 📱 页面结构

### 1. 主页面 (PasskeyExamplePage)
```dart
// 简化的UI，只有基本输入
- 邮箱输入框
- 密码输入框
- 注册账户按钮
- 登录账户按钮
```

### 2. OTP验证页面 (OtpVerificationPage)
```dart
// 专门的OTP验证页面
- 邮箱显示
- 验证码输入框
- 重新发送按钮（带倒计时）
- 验证并继续按钮
```

### 3. Passkey认证页面 (PasskeyAuthPage)
```dart
// 专门的Passkey操作页面
- Passkey图标和说明
- 创建/使用Passkey按钮
- 状态提示
```

## 🔧 控制器方法

### 新增的流程控制方法

```dart
// 开始注册流程
Future<void> startRegister(String email, String password) async {
  // 验证输入 → 跳转到OTP页面
}

// 开始登录流程
Future<void> startLogin(String email, String password) async {
  // 验证输入 → 跳转到OTP页面
}

// 内部Passkey方法（在OTP验证后调用）
Future<void> register() async {
  // 创建Passkey → 完成注册
}

Future<void> login() async {
  // 使用Passkey → 完成登录
}
```

### OTP相关方法（保持不变）

```dart
Future<void> sendEmailOtp() async;     // 发送验证码
Future<bool> verifyEmailOtp() async;   // 验证验证码
```

## 🎯 用户体验

### 优势
1. **流程清晰**：每个步骤都有专门的页面
2. **安全性高**：邮箱+密码+OTP+Passkey 四重验证
3. **引导明确**：用户知道每一步在做什么
4. **测试友好**：每个步骤都有测试数据提示

### 测试数据
- **密码**：`123456`
- **OTP验证码**：`123456`
- **邮箱**：任意有效格式

## 🔄 页面导航流程

```
PasskeyExamplePage (主页)
    ↓ 点击注册/登录
OtpVerificationPage (OTP验证)
    ↓ 验证成功
PasskeyAuthPage (Passkey认证)
    ↓ 认证成功
PasskeyExamplePage (返回主页，已登录状态)
```

## 📋 后端接口调用顺序

### 注册流程
1. `sendEmailOtp()` - 发送验证码
2. `verifyEmailOtp()` - 验证验证码
3. `registerInit()` - 获取Passkey选项
4. `registerFinish()` - 完成注册

### 登录流程
1. `sendEmailOtp()` - 发送验证码
2. `verifyEmailOtp()` - 验证验证码
3. `loginInit()` - 获取Passkey选项
4. `loginFinish()` - 完成登录

## 🎨 UI特点

### 主页面
- 简洁的输入表单
- 清晰的按钮标识
- 测试提示信息

### OTP页面
- 大图标和清晰说明
- 倒计时重发机制
- 友好的错误提示

### Passkey页面
- 生物识别图标
- 操作说明文字
- 状态反馈

## 🔒 安全特性

1. **多因子认证**：
   - 知识因子：密码
   - 拥有因子：邮箱验证
   - 生物因子：Passkey

2. **流程控制**：
   - 必须按顺序完成每个步骤
   - 每个步骤都有验证

3. **数据保护**：
   - 密码RSA加密传输
   - 设备指纹绑定
   - Passkey本地存储

## 🚀 实现完成

✅ **主页面**：邮箱+密码输入，流程启动
✅ **OTP页面**：验证码发送和验证
✅ **Passkey页面**：生物识别认证
✅ **控制器**：完整的流程控制逻辑
✅ **API服务**：所有必需的后端接口
✅ **导航**：页面间的跳转逻辑

现在用户可以体验完整的：**邮箱+密码 → OTP验证 → Passkey认证** 流程！
