# AuthController 使用指南

## 概述

`AuthController` 是专门负责用户认证的控制器，与 `AppController` 分工明确：

- **AuthController**：专注认证逻辑（登录、登出、令牌管理）
- **AppController**：负责应用级别的配置和初始化

## 架构设计

```
AppController (应用控制器)
    ├── 初始化 AuthController
    ├── 提供快速访问方法
    └── 管理应用级配置

AuthController (认证控制器)
    ├── 登录状态管理
    ├── 令牌自动刷新
    ├── 用户信息管理
    └── 登录/登出逻辑
```

## 初始化和注册

### 1. 在 main.dart 中注册

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化存储
  await GetStorage.init();
  
  // 注册控制器
  Get.put(AppController(), permanent: true);
  // AuthController 会在 AppController 中自动初始化
  
  runApp(MyApp());
}
```

### 2. 在应用启动时检查认证状态

```dart
class SplashPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // 检查认证状态并导航
    Future.delayed(Duration(seconds: 2), () {
      final authController = AuthController.to;
      if (authController.isAuthenticated) {
        Get.offAllNamed(Routes.home);
      } else {
        Get.offAllNamed(Routes.login);
      }
    });
    
    return Scaffold(
      body: Center(child: CircularProgressIndicator()),
    );
  }
}
```

## 使用方法

### 1. 登录成功处理

```dart
class LoginController extends BaseController {
  final AuthController authController = AuthController.to;
  
  Future<void> login(String email, String password) async {
    try {
      // 调用登录API
      final result = await loginApi(email, password);
      
      // 处理登录成功
      await authController.handleLoginSuccess({
        'accessToken': result['accessToken'],
        'refreshToken': result['refreshToken'],
        'user': result['user'],
      });
      
      // AuthController 会自动导航到主页
    } catch (e) {
      Get.snackbar('登录失败', e.toString());
    }
  }
}
```

### 2. 检查登录状态

```dart
class HomeController extends BaseController {
  final AuthController authController = AuthController.to;
  
  @override
  void onInit() {
    super.onInit();
    
    // 检查是否已登录
    if (!authController.isAuthenticated) {
      authController.requireLogin(message: '请先登录访问此页面');
      return;
    }
    
    // 已登录，加载数据
    loadUserData();
  }
  
  void loadUserData() {
    final userEmail = authController.userEmail;
    final userName = authController.userDisplayName;
    // 使用用户信息
  }
}
```

### 3. 登出操作

```dart
class ProfileController extends BaseController {
  final AuthController authController = AuthController.to;
  
  Future<void> logout() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: Text('确认登出'),
        content: Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text('确定'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      await authController.logout();
      // AuthController 会自动导航到登录页
    }
  }
}
```

### 4. API请求中的认证

```dart
class ApiService {
  static Future<Map<String, dynamic>> authenticatedRequest(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    final authController = AuthController.to;
    
    // 检查认证状态
    if (!authController.isAuthenticated) {
      authController.requireLogin();
      throw Exception('未登录');
    }
    
    // 检查令牌有效性
    final isValid = await authController.checkTokenValidity();
    if (!isValid) {
      throw Exception('认证失败');
    }
    
    // 使用令牌发送请求
    final token = authController.accessToken.value;
    return await httpRequest(endpoint, data, headers: {
      'Authorization': 'Bearer $token',
    });
  }
}
```

### 5. 响应式UI更新

```dart
class UserInfoWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final authController = AuthController.to;
    
    return Obx(() {
      if (!authController.isLoggedIn.value) {
        return LoginPromptWidget();
      }
      
      final user = authController.currentUser.value;
      return Column(
        children: [
          Text('欢迎，${user?['displayName'] ?? '用户'}'),
          Text('邮箱：${user?['email'] ?? ''}'),
          ElevatedButton(
            onPressed: () => authController.logout(),
            child: Text('登出'),
          ),
        ],
      );
    });
  }
}
```

## 快速访问方法

### 通过 AppController 访问

```dart
class SomeController extends BaseController {
  final AppController appController = Get.find<AppController>();
  
  void someMethod() {
    // 快速检查登录状态
    if (appController.isLoggedIn) {
      // 已登录逻辑
    } else {
      // 未登录逻辑
    }
    
    // 快速登出
    appController.logout();
  }
}
```

### 直接访问 AuthController

```dart
class AnotherController extends BaseController {
  void anotherMethod() {
    // 直接访问
    final authController = AuthController.to;
    
    // 获取用户信息
    final userEmail = authController.userEmail;
    final isRefreshing = authController.isRefreshing.value;
    
    // 监听认证状态变化
    ever(authController.isLoggedIn, (isLoggedIn) {
      if (isLoggedIn) {
        // 登录成功处理
      } else {
        // 登出处理
      }
    });
  }
}
```

## 最佳实践

### 1. 控制器职责分离
- AuthController 只处理认证相关逻辑
- 业务控制器通过 AuthController 获取认证状态
- AppController 提供便捷的访问方法

### 2. 错误处理
```dart
// 在需要认证的页面
@override
void onInit() {
  super.onInit();
  
  if (!AuthController.to.isAuthenticated) {
    AuthController.to.requireLogin(message: '访问此功能需要登录');
    return;
  }
}
```

### 3. 令牌自动刷新
AuthController 会自动处理令牌刷新，业务代码无需关心。

### 4. 状态监听
```dart
// 监听登录状态变化
ever(AuthController.to.isLoggedIn, (isLoggedIn) {
  if (!isLoggedIn) {
    // 清理用户相关数据
    clearUserCache();
  }
});
```

## 总结

这种设计的优势：
1. **职责清晰**：认证逻辑集中在 AuthController
2. **易于使用**：提供多种访问方式
3. **自动管理**：令牌刷新和状态同步自动处理
4. **响应式**：UI 可以响应认证状态变化
5. **可扩展**：易于添加新的认证功能
