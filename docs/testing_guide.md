# 测试指南 - Passkey认证流程

## 🧪 测试环境

现在所有的API接口都有**模拟响应**，即使后端还没有实现，你也可以测试完整的流程！

## 📱 测试步骤

### 1. 注册流程测试

1. **打开应用** → 进入Passkey示例页面
2. **输入信息**：
   - 邮箱：`<EMAIL>`（任意有效格式）
   - 密码：`123456`
3. **点击"注册账户"** → 自动跳转到OTP验证页面
4. **OTP验证页面**：
   - 会自动发送验证码（模拟）
   - 输入验证码：`123456`
   - 点击"验证并继续"
5. **Passkey创建页面**：
   - 点击"创建 Passkey"
   - 会模拟生物识别过程（2秒延迟）
   - 注册成功！

### 2. 登录流程测试

1. **输入信息**：
   - 邮箱：`<EMAIL>`
   - 密码：`123456`
2. **点击"登录账户"** → 跳转到OTP验证页面
3. **OTP验证**：输入 `123456` → 验证成功
4. **Passkey认证**：点击"使用 Passkey 登录" → 登录成功

## 🔧 模拟响应说明

### API接口模拟

所有API接口现在都有fallback模拟响应：

```dart
// 当真实API返回404时，自动使用模拟响应
try {
  return await realApiCall();
} catch (e) {
  Log.e("API失败，使用模拟响应: $e");
  return mockResponse();
}
```

### 测试数据

- **密码**：`123456`
- **OTP验证码**：`123456`
- **邮箱**：任意有效格式
- **Passkey**：模拟2秒生物识别过程

### 模拟的响应数据

#### 注册/登录成功响应：
```json
{
  "success": true,
  "accessToken": "mock_access_token_1234567890",
  "refreshToken": "mock_refresh_token_1234567890",
  "user": {
    "id": "user_12345",
    "email": "<EMAIL>",
    "displayName": "test"
  }
}
```

#### WebAuthn选项：
```json
{
  "success": true,
  "webauthnOptions": {
    "challenge": "mock_challenge_1234567890",
    "rp": {"id": "your-domain.com", "name": "VCB App"},
    "user": {"id": "user_12345", "name": "<EMAIL>"},
    "timeout": 60000
  }
}
```

## 📊 日志输出

测试时可以在控制台看到详细日志：

```
I/flutter: │ 📝 注册初始化失败，使用模拟响应: Exception: HTTP 404
I/flutter: │ 📝 模拟Passkey注册: <EMAIL>
I/flutter: │ 📝 Passkey注册成功: <EMAIL>
I/flutter: │ 📝 注册完成失败，使用模拟响应: Exception: HTTP 404
```

## 🎯 测试重点

### 1. 流程完整性
- ✅ 邮箱+密码输入
- ✅ OTP验证页面跳转
- ✅ Passkey认证页面跳转
- ✅ 成功后返回主页

### 2. 状态管理
- ✅ 按钮状态变化（loading/enable）
- ✅ 错误消息显示
- ✅ 成功消息提示
- ✅ 页面导航正确

### 3. 用户体验
- ✅ 清晰的页面标题和说明
- ✅ 友好的错误提示
- ✅ 测试数据提示
- ✅ 倒计时功能

## 🔄 重置测试

如果需要重新测试：

1. **重启应用** 或 **热重载**
2. **清除状态**：控制器会自动重置
3. **重新输入**：使用相同的测试数据

## 🚀 后端集成

当后端API准备好时：

1. **移除模拟响应**：删除try-catch中的模拟部分
2. **更新API地址**：修改`baseUrl`为真实地址
3. **测试真实流程**：使用相同的测试步骤

## 📝 测试检查清单

- [ ] 邮箱输入验证
- [ ] 密码输入验证
- [ ] OTP发送功能
- [ ] OTP验证功能
- [ ] OTP倒计时功能
- [ ] Passkey注册流程
- [ ] Passkey登录流程
- [ ] 页面导航正确
- [ ] 状态消息显示
- [ ] 错误处理正常

## 🎉 预期结果

完成测试后，你应该能看到：

1. **完整的三步流程**：邮箱+密码 → OTP → Passkey
2. **清晰的用户引导**：每个页面都有明确的说明
3. **友好的反馈**：成功/失败消息，按钮状态变化
4. **模拟的真实体验**：接近真实的认证流程

现在你可以完整测试整个Passkey认证流程了！🎊
