import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

val keystoreProperties = Properties()
val keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.vcb.finance"
    compileSdk = 36
    ndkVersion = "28.0.12674087"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_21.toString()
    }

    defaultConfig {
        applicationId = "com.vcb.finance"
        minSdk = 26
        targetSdk = 36
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled = true

        ndk.abiFilters += listOf("armeabi-v7a", "arm64-v8a", "x86_64")

        manifestPlaceholders["JPUSH_PKGNAME"] =  "com.vcb.finance"
        manifestPlaceholders["JPUSH_APPKEY"] = "0dc0c7099e7c801bc172dbe4"
        manifestPlaceholders["JPUSH_CHANNEL"] = "developer-default"
        manifestPlaceholders["MEIZU_APPKEY"] = "MZ-魅族的APPKEY"
        manifestPlaceholders["MEIZU_APPID"] = "MZ-魅族的APPID"
        manifestPlaceholders["XIAOMI_APPID"] = "MI-小米的APPID"
        manifestPlaceholders["XIAOMI_APPKEY"] = "MI-小米的APPKEY"
        manifestPlaceholders["OPPO_APPKEY"] = "OP-oppo的APPKEY"
        manifestPlaceholders["OPPO_APPID"] = "OP-oppo的APPID"
        manifestPlaceholders["OPPO_APPSECRET"] = "OP-oppo的APPSECRET"
        manifestPlaceholders["VIVO_APPKEY"] = "vivo的APPKEY"
        manifestPlaceholders["VIVO_APPID"] = "vivo的APPID"
        manifestPlaceholders["HONOR_APPID"] = "Honor的APP ID"
    }

    

    packaging {
        resources {
            excludes.add("lib/x86_64/darwin/libscrypt.dylib")
            excludes.add("lib/x86_64/freebsd/libscrypt.so")
            excludes.add("lib/x86_64/linux/libscrypt.so")
        }
    }

    signingConfigs {
        create("release") {
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = keystoreProperties["storeFile"]?.let { file(it) }
            storePassword = keystoreProperties["storePassword"] as String
        }
    }

    lint {
        checkReleaseBuilds = false
    }

    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            signingConfig = signingConfigs.getByName("release")
        }
        getByName("debug") {
            signingConfig = signingConfigs.getByName("release")
        }
    }

    applicationVariants.all {
        val variant = this
        outputs.all {
            (this as com.android.build.gradle.internal.api.BaseVariantOutputImpl).outputFileName =
                "VCB-${variant.versionName}.${variant.versionCode}.${variant.buildType.name}.apk"
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation("com.android.support:multidex:1.0.3")
}