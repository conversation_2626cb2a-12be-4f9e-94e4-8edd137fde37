import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:vcb/main.test.dart' as app;
import 'package:vcb/utils/log_utils.dart';

import 'splash_screen_test_case.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  testWidgets('正在自动化测试...', (WidgetTester tester) async {
    app.main();
    await tester.pumpAndSettle();
    await SplashScreenTest.runTest(tester);
    //   await GuidePageTest.runTest(tester);

    Log.g("所有测试用例执行完成！");
  });
}
