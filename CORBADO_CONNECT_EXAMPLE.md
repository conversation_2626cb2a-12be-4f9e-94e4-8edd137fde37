# Corbado Connect 模式 Flutter 完整示例

## 📋 概述

这是一个完整的 Corbado Connect 模式 Flutter 示例，展示了如何在 Flutter 应用中集成现代化的用户认证体验。

## 🏗️ 项目结构

```
lib/modules/corbado/
├── corbado_controller.dart      # Corbado 控制器
├── corbado_page.dart           # Corbado 主页面
├── example_integration.dart    # 集成示例
└── README.md                   # 详细说明文档

lib/route/
├── routes.dart                 # 路由定义（已更新）
└── pages.dart                  # 页面配置（已更新）
```

## 🚀 快速开始

### 1. 依赖配置

项目已包含必要的依赖：

```yaml
dependencies:
  corbado_auth: ^3.4.1
  get: ^4.7.2
  flutter_easyloading: ^3.0.5
```

### 2. 项目配置

在 `lib/modules/corbado/corbado_controller.dart` 中配置您的 Corbado 项目：

```dart
class CorbadoController extends BaseController {
  // 替换为您的项目ID
  static const String projectId = 'your-project-id';
}
```

### 3. 路由访问

```dart
// 导航到 Corbado 页面
Get.toNamed(AppRoutes.corbadoPage);
```

## 📱 功能特性

### ✅ 核心功能

- **Corbado Connect 模式集成**: 完整的认证流程
- **多种登录方式**: 密码、生物识别、无密码、社交登录
- **状态管理**: 使用 GetX 进行响应式状态管理
- **错误处理**: 完善的错误提示和处理机制
- **UI 设计**: 现代化的用户界面设计

### 🔧 技术特性

- **响应式设计**: 适配不同屏幕尺寸
- **主题支持**: 支持明暗主题切换
- **国际化**: 支持多语言显示
- **可扩展性**: 易于扩展和自定义

## 🎯 使用场景

### 1. 独立认证页面

```dart
// 直接访问 Corbado 认证页面
Get.toNamed(AppRoutes.corbadoPage);
```

### 2. 集成到现有登录流程

```dart
// 在现有登录页面中添加 Corbado 选项
ButtonWidget(
  text: '使用 Corbado 登录',
  onPressed: () => Get.toNamed(AppRoutes.corbadoPage),
),
```

### 3. 个人中心状态显示

```dart
// 在个人中心显示认证状态
Obx(() {
  final controller = Get.find<CorbadoController>();
  return Text('认证状态: ${controller.isUserAuthenticated ? "已认证" : "未认证"}');
}),
```

## 🔄 工作流程

### 基本流程

1. **初始化**: 调用 `initializeCorbado()` 初始化 Corbado
2. **选择操作**: 用户选择登录或注册
3. **认证流程**: 系统打开 Corbado 认证界面
4. **完成认证**: 用户完成认证流程
5. **返回应用**: 认证成功后返回应用

### 状态管理

```dart
// 加载状态
final isLoading = false.obs;

// 认证状态
final isAuthenticated = false.obs;

// 错误信息
final errorMessage = ''.obs;
```

## 🎨 UI 组件

### 主要页面组件

- **状态卡片**: 显示当前认证状态
- **操作按钮**: 初始化、登录、注册、登出
- **错误提示**: 显示错误信息
- **说明信息**: 使用指南

### 自定义样式

```dart
// 自定义按钮样式
ButtonWidget(
  text: '开始登录',
  onPressed: controller.startLogin,
  // 可以添加自定义参数
),
```

## 🔧 配置选项

### Corbado 配置

```dart
class CorbadoController extends BaseController {
  // 项目配置
  static const String projectId = 'your-project-id';
  static const bool CORBADO_TELEMETRY_DISABLED = true;
  
  // 初始化配置
  Future<void> initializeCorbado() async {
    _corbadoAuth = CorbadoAuth();
    await _corbadoAuth!.init(projectId: projectId);
  }
}
```

### 路由配置

```dart
// 在 lib/route/pages.dart 中
GetPage(
  name: AppRoutes.corbadoPage,
  page: () => const CorbadoPage(),
  binding: CorbadoBinding(),
),
```

## 📊 示例代码

### 控制器示例

```dart
class CorbadoController extends BaseController {
  // 初始化
  Future<void> initializeCorbado() async { ... }
  
  // 登录
  Future<void> startLogin() async { ... }
  
  // 注册
  Future<void> startRegister() async { ... }
  
  // 状态检查
  bool get isUserAuthenticated => isAuthenticated.value;
}
```

### 页面示例

```dart
class CorbadoPage extends BaseStatelessWidget<CorbadoController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: 'Corbado Connect'),
      body: Obx(() => _buildBody()),
    );
  }
}
```

## 🛠️ 自定义扩展

### 添加新的认证方式

```dart
// 在控制器中添加新方法
Future<void> startCustomAuth() async {
  // 实现自定义认证逻辑
}
```

### 自定义 UI 组件

```dart
// 创建自定义认证按钮
Widget _buildCustomAuthButton() {
  return Container(
    // 自定义样式
  );
}
```

## 🔍 调试和测试

### 调试技巧

1. **检查初始化**: 确保项目 ID 正确
2. **网络连接**: 确认网络权限和连接
3. **控制台日志**: 查看详细的错误信息
4. **状态检查**: 使用 `controller.isUserAuthenticated` 检查状态

### 测试流程

1. **单元测试**: 测试控制器方法
2. **集成测试**: 测试完整的认证流程
3. **UI 测试**: 测试页面交互
4. **端到端测试**: 测试完整的用户流程

## 📚 相关文档

- [Corbado 官方文档](https://docs.corbado.com/)
- [Flutter 认证最佳实践](https://flutter.dev/docs/development/data-and-backend/state-mgmt/simple)
- [GetX 状态管理](https://pub.dev/packages/get)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

---

**注意**: 这是一个完整的示例项目，实际使用时请根据您的具体需求进行调整和优化。 