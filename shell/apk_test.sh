#!/bin/bash

echo "开始打包FireBase 自动化测试Android apk,仅用来测试不可发布"



# 切换到上一级目录
cd ..

# 设置脚本遇到错误时停止执行
set -e

# 保存当前目录
current_dir=$(pwd)

# 清理 Flutter 项目
echo "正在清理 Flutter 项目..."
flutter clean


# 打包 APK
echo "开始构建apk..."
flutter build apk --release --target=lib/main.test.dart


# 打包完成
echo "APK 打包完成，文件位于: $current_dir/build/app/outputs/apk/release/"

# 打开 APK 输出文件夹
open "./build/app/outputs/apk/release/"
