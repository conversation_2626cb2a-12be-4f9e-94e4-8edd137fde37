#!/bin/bash
echo "ios 一键三连"

cd ..
set -e

# Save current directory
current_dir=$(pwd)

# Clean the Flutter project
echo "Cleaning Flutter project..."
flutter clean

# Get all the dependencies
echo "Getting dependencies..."
flutter pub get

# Navigate to the iOS directory which is assumed to be at the parent directory of where the script is located
echo "Navigating to the iOS directory..."


if [ -d "ios" ]; then
  cd ios
  
  # Install CocoaPods dependencies
  echo "Installing CocoaPods dependencies..."
  pod install

  # Go back to the original directory
  cd "$current_dir"
  echo "Setup completed successfully."
else
  echo "Error: 'ios' directory does not exist."
fi
